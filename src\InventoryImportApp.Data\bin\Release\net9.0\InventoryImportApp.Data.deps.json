{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"InventoryImportApp.Data/1.0.0": {"dependencies": {"InventoryImportApp.Core": "1.0.0"}, "runtime": {"InventoryImportApp.Data.dll": {}}}, "InventoryImportApp.Core/1.0.0": {"runtime": {"InventoryImportApp.Core.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"InventoryImportApp.Data/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "InventoryImportApp.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}