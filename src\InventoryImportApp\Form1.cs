using System.Text.Json;
using InventoryImportApp.Core.Models;
using InventoryImportApp.Core.Services;
using NLog;

namespace InventoryImportApp;

public partial class Form1 : Form
{
    private AppSettings _appSettings;
    private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
    private FileDiscoveryService _fileDiscoveryService;
    private List<InventoryFileInfo> _inventoryFiles = new();

    public Form1()
    {
        InitializeComponent();
        InitializeLogging();
        LoadConfiguration();
        InitializeServices();
        InitializeForm();
    }

    private void InitializeLogging()
    {
        try
        {
            // ログディレクトリを作成
            var logDir = Path.Combine(Application.StartupPath, "logs");
            if (!Directory.Exists(logDir))
            {
                Directory.CreateDirectory(logDir);
            }

            _logger.Info("=== アプリケーション開始 ===");
            _logger.Info($"実行ファイルパス: {Application.StartupPath}");
            _logger.Info($"ログファイルパス: {logDir}");
        }
        catch (Exception ex)
        {
            MessageBox.Show($"ログ初期化エラー: {ex.Message}", "エラー", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private void LoadConfiguration()
    {
        var configPath = Path.Combine(Application.StartupPath, "Config", "appsettings.json");
        
        try
        {
            if (File.Exists(configPath))
            {
                // JSON設定ファイルを読み込み
                var jsonString = File.ReadAllText(configPath);
                _appSettings = JsonSerializer.Deserialize<AppSettings>(jsonString, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                
                // デシリアライゼーション結果の検証
                if (_appSettings == null)
                {
                    throw new InvalidOperationException("JSON設定のデシリアライゼーションに失敗しました");
                }
                
                // 必須項目の検証
                if (string.IsNullOrEmpty(_appSettings.InventoryImportApp.DatabaseConnectionString))
                {
                    throw new InvalidOperationException("DatabaseConnectionStringが設定されていません");
                }

                // 読み込み成功（メッセージ表示なし）
            }
            else
            {
                throw new FileNotFoundException($"設定ファイルが見つかりません: {configPath}");
            }
        }
        catch (Exception ex)
        {
            // エラー詳細を表示
            MessageBox.Show($"設定ファイルの読み込みエラー\n\n" +
                          $"エラー: {ex.Message}\n" +
                          $"設定ファイルパス: {configPath}\n" +
                          $"ファイル存在: {File.Exists(configPath)}\n\n" +
                          "デフォルト設定で続行します。",
                          "設定エラー",
                          MessageBoxButtons.OK,
                          MessageBoxIcon.Warning);
            
            // 最後の手段としてデフォルト設定を使用
            _appSettings = new AppSettings
            {
                InventoryImportApp = new InventoryImportAppSettings
                {
                    DefaultParentFolder = "C:\\YSInventoryUpdate\\out",
                                            DatabaseConnectionString = "Data Source=***********;Initial Catalog=YsskKikan;User ID=sa;Password=**********;Connection Timeout=30;Command Timeout=300;TrustServerCertificate=True;Encrypt=False;MultipleActiveResultSets=True;",
                    TargetStores = new List<string> { "347", "351", "353", "355", "360", "362", "363", "364", "365", "366", "367", "443", "444" },
                    UI = new UISettings
                    {
                        WindowTitle = "棚卸結果取込システム",
                        GridPageSize = 1000,
                        ConfirmationRequired = true
                    },
                    Import = new ImportSettings
                    {
                        MaxFileSize = 52428800,
                        SupportedEncodings = new List<string> { "UTF-8", "Shift_JIS" },
                        RequiredColumns = new List<string> { "店舗コード", "棚卸日", "商品コード", "在庫数" }
                    }
                }
            };
        }
    }

    private void InitializeServices()
    {
        _fileDiscoveryService = new FileDiscoveryService(_appSettings.InventoryImportApp.TargetStores);
    }

    private void InitializeForm()
    {
        Text = "棚卸結果取込システム";
        
        // ListView列の設定
        fileListView.Columns.Add("店舗", 80);
        fileListView.Columns.Add("ファイル名", 200);
        fileListView.Columns.Add("サイズ", 80);
        fileListView.Columns.Add("更新日時", 140);
        fileListView.Columns.Add("レコード数", 100);
        fileListView.Columns.Add("パス", 300);
        
        // イベントハンドラーの設定
        browseFolderButton.Click += BrowseFolderButton_Click;
        refreshButton.Click += RefreshButton_Click;
        selectAllCheckBox.CheckedChanged += SelectAllCheckBox_CheckedChanged;
        fileListView.ItemChecked += FileListView_ItemChecked;
        importButton.Click += ImportButton_Click;
        productionRegisterButton.Click += ProductionRegisterButton_Click;
        Resize += Form1_Resize;
        
        // DateTimePickerの初期設定
        registrationDatePicker.MinDate = DateTime.Today.AddDays(-1);
        registrationDatePicker.MaxDate = DateTime.Today.AddDays(1);
        registrationDatePicker.Value = DateTime.Today;
        registrationDatePicker.Format = DateTimePickerFormat.Short;
        
        // DataGridViewの初期設定
        dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
        dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
        dataGridView.AllowUserToResizeRows = false;
        dataGridView.RowHeadersVisible = false;
        
        // 初期状態設定
        importButton.Enabled = false;
        progressBar.Visible = false;
        dataConfirmationLabel.Visible = false;
        dataGridView.Visible = false;
        productionRegisterButton.Visible = false;
        
        // 初期フォルダパスの設定と初回スキャン
        folderPathTextBox.Text = _appSettings.InventoryImportApp.DefaultParentFolder;
        RefreshFileList();
    }

    private async void RefreshFileList()
    {
        try
        {
            statusLabel.Text = "ファイルを検索中...";
            progressBar.Visible = true;
            progressBar.Style = ProgressBarStyle.Marquee;
            
            refreshButton.Enabled = false;
            browseFolderButton.Enabled = false;
            
            _inventoryFiles = await _fileDiscoveryService.DiscoverInventoryFilesAsync(folderPathTextBox.Text);
            
            UpdateFileListView();
            UpdateSelectedCount();
            
            statusLabel.Text = $"ファイル検索完了 - {_inventoryFiles.Count}件のファイルが見つかりました";
        }
        catch (Exception ex)
        {
            statusLabel.Text = $"エラー: {ex.Message}";
            MessageBox.Show($"ファイル検索中にエラーが発生しました:\n{ex.Message}", "エラー", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            progressBar.Visible = false;
            refreshButton.Enabled = true;
            browseFolderButton.Enabled = true;
        }
    }

    private void UpdateFileListView()
    {
        fileListView.Items.Clear();
        
        foreach (var fileInfo in _inventoryFiles)
        {
            var item = new ListViewItem(fileInfo.StoreCode);
            item.SubItems.Add(fileInfo.FileName);
            item.SubItems.Add(fileInfo.FormattedFileSize);
            item.SubItems.Add(fileInfo.FormattedLastModified);
            item.SubItems.Add(fileInfo.RecordCount.ToString("#,0"));
            item.SubItems.Add(fileInfo.FilePath);
            item.Tag = fileInfo;
            
            fileListView.Items.Add(item);
        }
        
        // 列幅を自動調整
        AdjustColumnWidths();
    }

    private void AdjustColumnWidths()
    {
        // フォームサイズに合わせて列幅を動的調整
        var totalWidth = fileListView.ClientSize.Width - 25; // スクロールバー分を考慮
        
        if (fileListView.Columns.Count >= 6)
        {
            var storeWidth = Math.Max(80, (int)(totalWidth * 0.08));
            var fileNameWidth = Math.Max(200, (int)(totalWidth * 0.30));
            var sizeWidth = Math.Max(80, (int)(totalWidth * 0.10));
            var dateWidth = Math.Max(140, (int)(totalWidth * 0.18));
            var recordWidth = Math.Max(100, (int)(totalWidth * 0.12));
            var pathWidth = Math.Max(150, totalWidth - storeWidth - fileNameWidth - sizeWidth - dateWidth - recordWidth);
            
            fileListView.Columns[0].Width = storeWidth;
            fileListView.Columns[1].Width = fileNameWidth;
            fileListView.Columns[2].Width = sizeWidth;
            fileListView.Columns[3].Width = dateWidth;
            fileListView.Columns[4].Width = recordWidth;
            fileListView.Columns[5].Width = pathWidth;
        }
    }

    private void UpdateSelectedCount()
    {
        var selectedCount = _inventoryFiles.Count(f => f.IsSelected);
        var totalCount = _inventoryFiles.Count;
        selectedCountLabel.Text = $"選択数: {selectedCount} / {totalCount}";
        importButton.Enabled = selectedCount > 0;
        
        // インポートボタンの色を選択状態に応じて変更
        if (selectedCount > 0)
        {
            importButton.BackColor = Color.FromArgb(0, 120, 215);
            importButton.Text = $"インポート ({selectedCount}件)";
        }
        else
        {
            importButton.BackColor = Color.Gray;
            importButton.Text = "インポート";
        }
    }

    private void BrowseFolderButton_Click(object sender, EventArgs e)
    {
        using var dialog = new FolderBrowserDialog();
        dialog.Description = "棚卸データが格納されている親フォルダを選択してください";
        dialog.SelectedPath = folderPathTextBox.Text;
        
        if (dialog.ShowDialog() == DialogResult.OK)
        {
            folderPathTextBox.Text = dialog.SelectedPath;
            RefreshFileList();
        }
    }

    private void RefreshButton_Click(object sender, EventArgs e)
    {
        RefreshFileList();
    }

    private void SelectAllCheckBox_CheckedChanged(object sender, EventArgs e)
    {
        var isChecked = selectAllCheckBox.Checked;
        
        foreach (ListViewItem item in fileListView.Items)
        {
            item.Checked = isChecked;
            if (item.Tag is InventoryFileInfo fileInfo)
            {
                fileInfo.IsSelected = isChecked;
            }
        }
        
        UpdateSelectedCount();
    }

    private void FileListView_ItemChecked(object sender, ItemCheckedEventArgs e)
    {
        if (e.Item.Tag is InventoryFileInfo fileInfo)
        {
            fileInfo.IsSelected = e.Item.Checked;
        }
        
        UpdateSelectedCount();
        
        // すべて選択チェックボックスの状態を更新
        var checkedCount = fileListView.CheckedItems.Count;
        var totalCount = fileListView.Items.Count;
        
        selectAllCheckBox.CheckedChanged -= SelectAllCheckBox_CheckedChanged;
        if (checkedCount == 0)
        {
            selectAllCheckBox.CheckState = CheckState.Unchecked;
        }
        else if (checkedCount == totalCount)
        {
            selectAllCheckBox.CheckState = CheckState.Checked;
        }
        else
        {
            selectAllCheckBox.CheckState = CheckState.Indeterminate;
        }
        selectAllCheckBox.CheckedChanged += SelectAllCheckBox_CheckedChanged;
    }

    private async void ImportButton_Click(object sender, EventArgs e)
    {
        var selectedFiles = _inventoryFiles.Where(f => f.IsSelected).ToList();
        
        if (selectedFiles.Count == 0)
        {
            MessageBox.Show("インポートするファイルを選択してください。", "確認", MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }

        var result = MessageBox.Show(
            $"選択された {selectedFiles.Count} 件のファイルをインポートしますか？\n\n" +
            "処理手順:\n" +
            "1. InventoryAll_wk テーブル初期化\n" +
            "2. ItemInventories_wk テーブル初期化\n" +
            "3. CSVファイルを InventoryAll_wk に投入\n" +
            "4. マスタ結合して ItemInventories_wk に投入",
            "インポート確認",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);

        if (result == DialogResult.Yes)
        {
            await ExecuteImportProcessAsync(selectedFiles);
        }
    }

    private async Task ExecuteImportProcessAsync(List<InventoryFileInfo> selectedFiles)
    {
        var totalSteps = 5;
        var currentStep = 0;
        
        progressBar.Visible = true;
        progressBar.Maximum = 100;
        progressBar.Value = 0;
        
        // 選択された登録日を取得（YYYY/MM/DD 23:59:59形式）
        var registrationDate = registrationDatePicker.Value.Date.AddHours(23).AddMinutes(59).AddSeconds(59);
        
        try
        {
            var dbService = new DatabaseImportService(_appSettings.InventoryImportApp.DatabaseConnectionString);
            
            // プログレス報告用
            var progress = new Progress<ImportProgress>(p =>
            {
                statusLabel.Text = p.Phase;
                progressBar.Value = p.ProgressPercentage;
            });
            
            // DatabaseImportServiceで一括処理
            var result = await dbService.ImportSelectedFilesAsync(selectedFiles, progress, registrationDate);
            
            if (!result.IsSuccess)
            {
                throw new Exception(result.ErrorMessage);
            }
            
            var importedRecords = result.ImportedCsvRecords;
            var processedRecords = result.ProcessedRecords;
            
            // フェーズ5: 完了
            currentStep++;
            statusLabel.Text = "インポート処理完了";
            progressBar.Value = 100;
            
            MessageBox.Show($"インポート処理が完了しました。\n\n" +
                          $"- インポートしたCSVレコード数: {importedRecords:N0} 件\n" +
                          $"- 処理されたレコード数: {processedRecords:N0} 件\n" +
                          $"- 登録日時: {registrationDate:yyyy/MM/dd HH:mm:ss}",
                          "インポート完了",
                          MessageBoxButtons.OK,
                          MessageBoxIcon.Information);
                          
            // データ確認グリッドを表示
            await DisplayWorkDataAsync();
        }
        catch (Exception ex)
        {
            statusLabel.Text = "インポート処理でエラーが発生しました";
            progressBar.Value = 0;
            
            MessageBox.Show($"インポート処理中にエラーが発生しました：\n\n{ex.Message}",
                          "エラー",
                          MessageBoxButtons.OK,
                          MessageBoxIcon.Error);
        }
        finally
        {
            progressBar.Visible = false;
            importButton.Enabled = true;
        }
    }

    private async Task ClearWorkTablesAsync()
    {
        var dbService = new DatabaseImportService(_appSettings.InventoryImportApp.DatabaseConnectionString);
        await dbService.ClearWorkTablesAsync();
    }

    private async Task<int> ImportCsvFilesToInventoryAllWorkAsync(List<InventoryFileInfo> selectedFiles)
    {
        var totalRecords = 0;
        
        foreach (var fileInfo in selectedFiles)
        {
            statusLabel.Text = $"CSVファイル投入中: {fileInfo.FileName}";
            
            // TODO: データベース接続実装後に有効化
            /*
            using var connection = new Microsoft.Data.SqlClient.SqlConnection(_appSettings.InventoryImportApp.DatabaseConnectionString);
            await connection.OpenAsync();
            
            var lines = await File.ReadAllLinesAsync(fileInfo.FilePath);
            
            // ヘッダー行をスキップ（1行目）
            for (int i = 1; i < lines.Length; i++)
            {
                var line = lines[i].Trim();
                if (string.IsNullOrEmpty(line)) continue;
                
                // CSVの各列を解析（カンマ区切り、ダブルクォート対応）
                var columns = line.Split(',');
                if (columns.Length >= 4)
                {
                    var sql = @"INSERT INTO dbo.InventoryAll_wk ([列 0], [列 1], [列 2], [列 3]) 
                               VALUES (@Col0, @Col1, @Col2, @Col3)";
                    
                    using var cmd = new Microsoft.Data.SqlClient.SqlCommand(sql, connection);
                    cmd.Parameters.AddWithValue("@Col0", columns[0].Trim('"'));
                    cmd.Parameters.AddWithValue("@Col1", columns[1].Trim('"'));
                    cmd.Parameters.AddWithValue("@Col2", columns[2].Trim('"'));
                    cmd.Parameters.AddWithValue("@Col3", columns[3].Trim('"'));
                    
                    await cmd.ExecuteNonQueryAsync();
                    totalRecords++;
                }
            }
            */
            
            // 現在はシミュレーション
            totalRecords += fileInfo.RecordCount;
            await Task.Delay(200);
        }
        
        return totalRecords;
    }

    private async Task<int> ProcessInventoryDataToItemInventoriesWorkAsync(DateTime registrationDate)
    {
        // TODO: データベース接続実装後に有効化
        /*
        using var connection = new Microsoft.Data.SqlClient.SqlConnection(_appSettings.InventoryImportApp.DatabaseConnectionString);
        await connection.OpenAsync();
        
        // memo.txtの⑤番の処理に相当
        var sql = @"
            INSERT INTO ItemInventories_wk
                (StoreId, ItemId, InventoryCount, SectionId, LastUpdateUserId, DelFlg, InventoryDate, CrDateTime, UpDateTime)
            SELECT 
                s.Id as StoreId,
                i.Id as ItemId, 
                CAST([列 3] as int) as InventoryCount,
                i.SectionId,
                1 as LastUpdateUserId,
                0 as DelFlg,
                CAST([列 1] as date) as InventoryDate,
                GETDATE() as CrDateTime,
                GETDATE() as UpDateTime
            FROM dbo.InventoryAll_wk w
            INNER JOIN dbo.Stores s ON s.Code = w.[列 0]
            INNER JOIN dbo.Items i ON i.Code = w.[列 2]
            WHERE w.[列 0] IS NOT NULL 
              AND w.[列 1] IS NOT NULL
              AND w.[列 2] IS NOT NULL
              AND w.[列 3] IS NOT NULL";
        
        using var cmd = new Microsoft.Data.SqlClient.SqlCommand(sql, connection);
        var processedRecords = await cmd.ExecuteNonQueryAsync();
        
        return processedRecords;
        */
        
        // 現在はシミュレーション（約80%の成功率を想定）
        var estimatedProcessed = (int)(_inventoryFiles.Where(f => f.IsSelected).Sum(f => f.RecordCount) * 0.8);
        await Task.Delay(1500);
        
        return estimatedProcessed;
    }

    private async Task CompleteInventoryProcessingAsync(DateTime registrationDate)
    {
        // TODO: データベース接続実装後に有効化
        /*
        using var connection = new Microsoft.Data.SqlClient.SqlConnection(_appSettings.InventoryImportApp.DatabaseConnectionString);
        await connection.OpenAsync();
        
        // 在庫補完処理
        var sql1 = @"
            UPDATE ItemInventories_wk
            SET InventoryCount = (
                SELECT SUM(InventoryCount)
                FROM InventoryAll_wk
                WHERE StoreId = ItemInventories_wk.StoreId
                AND ItemId = ItemInventories_wk.ItemId
                AND InventoryDate = @RegistrationDate
            )
            WHERE InventoryDate = @RegistrationDate";
        
        using var cmd1 = new Microsoft.Data.SqlClient.SqlCommand(sql1, connection);
        cmd1.Parameters.AddWithValue("@RegistrationDate", registrationDate);
        await cmd1.ExecuteNonQueryAsync();
        
        // 売価更新処理
        var sql2 = @"
            UPDATE ItemInventories_wk
            SET SalePrice = (
                SELECT SalePrice
                FROM Items
                WHERE Id = ItemInventories_wk.ItemId
            )
            WHERE InventoryDate = @RegistrationDate";
        
        using var cmd2 = new Microsoft.Data.SqlClient.SqlCommand(sql2, connection);
        cmd2.Parameters.AddWithValue("@RegistrationDate", registrationDate);
        await cmd2.ExecuteNonQueryAsync();
        
        // 重複削除処理
        var sql3 = @"
            DELETE FROM ItemInventories_wk
            WHERE InventoryDate = @RegistrationDate
            AND StoreId IN (
                SELECT StoreId
                FROM InventoryAll_wk
                WHERE InventoryDate = @RegistrationDate
            )
            AND ItemId IN (
                SELECT ItemId
                FROM InventoryAll_wk
                WHERE InventoryDate = @RegistrationDate
            )";
        
        using var cmd3 = new Microsoft.Data.SqlClient.SqlCommand(sql3, connection);
        cmd3.Parameters.AddWithValue("@RegistrationDate", registrationDate);
        await cmd3.ExecuteNonQueryAsync();
        */
        
        // 現在はシミュレーション
        await Task.Delay(1000);
    }

    /// <summary>
    /// ItemInventories_wkのデータを確認グリッドに表示
    /// </summary>
    private async Task DisplayWorkDataAsync()
    {
        try
        {
            statusLabel.Text = "データ確認画面を準備中...";
            progressBar.Visible = true;
            progressBar.Value = 50;
            
            var dbService = new DatabaseImportService(_appSettings.InventoryImportApp.DatabaseConnectionString);
            
            // データ取得
            var workData = await dbService.GetItemInventoriesWorkDataAsync();
            var summary = await dbService.GetWorkDataSummaryAsync();
            
            // グリッドにデータバインド
            dataGridView.DataSource = workData;
            
            // 列設定
            SetupDataGridColumns();
            
            // 統計情報をラベルに表示
            dataConfirmationLabel.Text = $"インポート結果確認（ItemInventories_wk） - 総件数: {summary.TotalRecords:N0}件, 店舗数: {summary.StoreCount}店舗, 商品数: {summary.ItemCount:N0}商品";
            
            // UI表示切り替え
            dataConfirmationLabel.Visible = true;
            dataGridView.Visible = true;
            productionRegisterButton.Visible = true;
            
            statusLabel.Text = "データ確認準備完了 - 内容を確認して本番登録を実行してください";
            progressBar.Visible = false;
            
        }
        catch (Exception ex)
        {
            statusLabel.Text = "データ確認表示エラー";
            progressBar.Visible = false;
            
            MessageBox.Show($"データ確認画面の表示中にエラーが発生しました：\n\n{ex.Message}",
                          "エラー",
                          MessageBoxButtons.OK,
                          MessageBoxIcon.Error);
        }
    }
    
    /// <summary>
    /// DataGridViewの列設定
    /// </summary>
    private void SetupDataGridColumns()
    {
        if (dataGridView.Columns.Count > 0)
        {
            // 列幅調整
            dataGridView.Columns["Id"].Width = 60;
            dataGridView.Columns["InventoryDate"].Width = 100;
            dataGridView.Columns["StoreName"].Width = 120;
            dataGridView.Columns["StoreCode"].Width = 80;
            dataGridView.Columns["ItemName"].Width = 200;
            dataGridView.Columns["ItemCode"].Width = 100;
            dataGridView.Columns["SectionName"].Width = 100;
            dataGridView.Columns["InventoryCount"].Width = 80;
            dataGridView.Columns["UnitPrice"].Width = 80;
            dataGridView.Columns["Price"].Width = 80;
            dataGridView.Columns["UnitPriceTaxIn"].Width = 90;
            dataGridView.Columns["PriceTaxIn"].Width = 90;
            dataGridView.Columns["CrDateTime"].Width = 140;
            dataGridView.Columns["UpDateTime"].Width = 140;
            dataGridView.Columns["LastUpdateUserId"].Width = 80;
            
            // 列ヘッダー名を日本語に変更
            dataGridView.Columns["Id"].HeaderText = "ID";
            dataGridView.Columns["InventoryDate"].HeaderText = "棚卸日";
            dataGridView.Columns["StoreName"].HeaderText = "店舗名";
            dataGridView.Columns["StoreCode"].HeaderText = "店舗コード";
            dataGridView.Columns["ItemName"].HeaderText = "商品名";
            dataGridView.Columns["ItemCode"].HeaderText = "商品コード";
            dataGridView.Columns["SectionName"].HeaderText = "部門名";
            dataGridView.Columns["InventoryCount"].HeaderText = "在庫数";
            dataGridView.Columns["UnitPrice"].HeaderText = "単価";
            dataGridView.Columns["Price"].HeaderText = "金額";
            dataGridView.Columns["UnitPriceTaxIn"].HeaderText = "単価(税込)";
            dataGridView.Columns["PriceTaxIn"].HeaderText = "金額(税込)";
            dataGridView.Columns["CrDateTime"].HeaderText = "作成日時";
            dataGridView.Columns["UpDateTime"].HeaderText = "更新日時";
            dataGridView.Columns["LastUpdateUserId"].HeaderText = "更新ユーザー";
            
            // 数値列は右寄せ
            dataGridView.Columns["InventoryCount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dataGridView.Columns["UnitPrice"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dataGridView.Columns["Price"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dataGridView.Columns["UnitPriceTaxIn"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            dataGridView.Columns["PriceTaxIn"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            
            // 数値列にカンマ区切り表示
            dataGridView.Columns["InventoryCount"].DefaultCellStyle.Format = "N0";
            dataGridView.Columns["UnitPrice"].DefaultCellStyle.Format = "N0";
            dataGridView.Columns["Price"].DefaultCellStyle.Format = "N0";
            dataGridView.Columns["UnitPriceTaxIn"].DefaultCellStyle.Format = "N0";
            dataGridView.Columns["PriceTaxIn"].DefaultCellStyle.Format = "N0";
        }
    }
    
    /// <summary>
    /// 本番登録ボタンクリックイベント
    /// </summary>
    private async void ProductionRegisterButton_Click(object sender, EventArgs e)
    {
        // 確認ダイアログ
        var confirmResult = MessageBox.Show(
            "本番テーブル（ItemInventories）にデータを登録します。\n\n" +
            "この操作は取り消すことができません。\n" +
            "実行してもよろしいですか？",
            "本番登録確認",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Warning,
            MessageBoxDefaultButton.Button2);
        
        if (confirmResult != DialogResult.Yes)
        {
            return;
        }
        
        await ExecuteProductionRegisterAsync();
    }
    
    /// <summary>
    /// 本番登録処理実行
    /// </summary>
    private async Task ExecuteProductionRegisterAsync()
    {
        productionRegisterButton.Enabled = false;
        importButton.Enabled = false;
        progressBar.Visible = true;
        progressBar.Value = 0;
        
        try
        {
            var dbService = new DatabaseImportService(_appSettings.InventoryImportApp.DatabaseConnectionString);
            
            // プログレス報告用
            var progress = new Progress<ImportProgress>(p =>
            {
                statusLabel.Text = p.Phase;
                progressBar.Value = p.ProgressPercentage;
            });
            
            // 本番登録実行（現在はモック）
            var result = await dbService.RegisterToProductionAsync(progress);
            
            if (result.IsSuccess)
            {
                MessageBox.Show($"本番登録が完了しました。\n\n" +
                              $"登録件数: {result.RegisteredRecords:N0} 件",
                              "本番登録完了",
                              MessageBoxButtons.OK,
                              MessageBoxIcon.Information);
                              
                statusLabel.Text = "本番登録完了";
                
                // UI状態をリセット
                dataConfirmationLabel.Visible = false;
                dataGridView.Visible = false;
                productionRegisterButton.Visible = false;
                dataGridView.DataSource = null;
            }
            else
            {
                throw new Exception(result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            statusLabel.Text = "本番登録でエラーが発生しました";
            
            MessageBox.Show($"本番登録中にエラーが発生しました：\n\n{ex.Message}",
                          "エラー",
                          MessageBoxButtons.OK,
                          MessageBoxIcon.Error);
        }
        finally
        {
            productionRegisterButton.Enabled = true;
            importButton.Enabled = true;
            progressBar.Visible = false;
        }
    }

    protected override void OnLoad(EventArgs e)
    {
        base.OnLoad(e);
        
        // ItemCheckedイベントを追加
        fileListView.ItemChecked += FileListView_ItemChecked;
        
        // リサイズイベントを追加
        this.Resize += Form1_Resize;
        
        // 初期列幅調整
        AdjustColumnWidths();
    }

    private void Form1_Resize(object sender, EventArgs e)
    {
        if (WindowState == FormWindowState.Minimized) return;
        
        var formWidth = ClientSize.Width;
        var formHeight = ClientSize.Height;
        
        // 動的リサイズ対応
        var newWidth = Math.Max(formWidth - 40, 800);
        var newHeight = Math.Max(formHeight - 100, 600);
        
        // 既存コントロールのリサイズ
        folderPathTextBox.Width = newWidth - 220;
        browseFolderButton.Location = new Point(newWidth - 180, browseFolderButton.Location.Y);
        refreshButton.Location = new Point(newWidth - 80, refreshButton.Location.Y);
        
        fileListView.Width = newWidth;
        fileListView.Height = Math.Max(250, (formHeight - 400) / 2);
        
        // 登録日とインポートボタンの位置調整
        var importButtonY = fileListView.Bottom + 20;
        registrationDateLabel.Location = new Point(registrationDateLabel.Location.X, importButtonY);
        registrationDatePicker.Location = new Point(registrationDatePicker.Location.X, importButtonY - 2);
        importButton.Location = new Point(newWidth - 220, importButtonY - 10);
        
        // ステータスとプログレスバーの位置調整
        var statusY = importButtonY + 70;
        statusLabel.Location = new Point(20, statusY);
        progressBar.Location = new Point(20, statusY + 30);
        progressBar.Width = newWidth;
        
        // データ確認ラベルとグリッドの位置調整
        var gridY = statusY + 70;
        dataConfirmationLabel.Location = new Point(20, gridY);
        dataGridView.Location = new Point(20, gridY + 40);
        dataGridView.Width = newWidth;
        dataGridView.Height = Math.Max(200, formHeight - gridY - 120);
        
        // 本番登録ボタンの位置調整
        productionRegisterButton.Location = new Point(newWidth - 220, formHeight - 90);
        
        AdjustColumnWidths();
    }
}
