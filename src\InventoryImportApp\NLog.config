<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

  <targets>
    <!-- ファイル出力ターゲット -->
    <target xsi:type="File" 
            name="fileTarget"
            fileName="${basedir}/logs/InventoryImport_${shortdate}.log"
            layout="${longdate} [${level:uppercase=true}] ${logger} - ${message} ${exception:format=tostring}"
            archiveFileName="${basedir}/logs/archive/InventoryImport_{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="30"
            createDirs="true" />
    
    <!-- コンソール出力ターゲット（デバッグ用） -->
    <target xsi:type="Console" 
            name="consoleTarget"
            layout="${time} [${level:uppercase=true}] ${message}" />
  </targets>

  <rules>
    <!-- すべてのログをファイルに出力 -->
    <logger name="*" minlevel="Info" writeTo="fileTarget" />
    
    <!-- デバッグ時はコンソールにも出力 -->
    <logger name="*" minlevel="Debug" writeTo="consoleTarget" />
  </rules>
</nlog>
