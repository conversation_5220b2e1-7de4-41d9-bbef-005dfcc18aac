using InventoryImportApp.Core.Models;

namespace InventoryImportApp.Core.Services;

public class FileDiscoveryService
{
    private readonly List<string> _targetStores;
    
    public FileDiscoveryService(List<string> targetStores)
    {
        _targetStores = targetStores ?? new List<string>();
    }
    
    public async Task<List<InventoryFileInfo>> DiscoverInventoryFilesAsync(string parentFolderPath)
    {
        var fileInfos = new List<InventoryFileInfo>();
        
        if (!Directory.Exists(parentFolderPath))
        {
            return fileInfos;
        }
        
        await Task.Run(() =>
        {
            // 店舗コードディレクトリを検索
            var storeDirectories = Directory.GetDirectories(parentFolderPath)
                .Where(dir => 
                {
                    var dirName = Path.GetFileName(dir);
                    return _targetStores.Contains(dirName) && dirName.Length == 3;
                })
                .ToList();
            
            foreach (var storeDir in storeDirectories)
            {
                var storeCode = Path.GetFileName(storeDir);
                
                // 両方のパターンを検索して重複を除去
                var csvFiles = Directory.GetFiles(storeDir, "InventoryAll*.csv", SearchOption.TopDirectoryOnly)
                    .Concat(Directory.GetFiles(storeDir, "InventoryALL*.csv", SearchOption.TopDirectoryOnly))
                    .Distinct(StringComparer.OrdinalIgnoreCase) // ファイルパスで重複除去（大文字小文字を区別しない）
                    .ToList();
                
                foreach (var csvFile in csvFiles)
                {
                    var fileInfo = new FileInfo(csvFile);
                    var inventoryFileInfo = new InventoryFileInfo
                    {
                        StoreCode = storeCode,
                        FileName = fileInfo.Name,
                        FilePath = fileInfo.FullName,
                        FileSize = fileInfo.Length,
                        LastModified = fileInfo.LastWriteTime,
                        IsSelected = false,
                        RecordCount = CountCsvRecords(csvFile)
                    };
                    
                    fileInfos.Add(inventoryFileInfo);
                }
            }
        });
        
        // 最終的にファイルパスで重複を除去してからソート
        return fileInfos
            .GroupBy(f => f.FilePath, StringComparer.OrdinalIgnoreCase)
            .Select(g => g.First())
            .OrderBy(f => f.StoreCode)
            .ThenBy(f => f.FileName)
            .ToList();
    }
    
    private int CountCsvRecords(string csvFilePath)
    {
        try
        {
            return File.ReadAllLines(csvFilePath).Length - 1; // ヘッダー行を除く
        }
        catch
        {
            return 0;
        }
    }
} 