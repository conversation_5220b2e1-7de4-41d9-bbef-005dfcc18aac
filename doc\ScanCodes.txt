USE [Yss<PERSON><PERSON><PERSON><PERSON>]
GO

/****** Object:  Table [dbo].[ScanCodes]    Script Date: 2025/07/30 17:42:18 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ScanCodes](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[ItemId] [bigint] NOT NULL,
	[ScanCode] [varchar](13) NOT NULL,
	[TypicalFlg] [bit] NOT NULL,
	[NonPluFlg] [bit] NOT NULL,
	[DelFlg] [bigint] NOT NULL,
	[CrDateTime] [datetime] NOT NULL,
	[UpDateTime] [datetime] NOT NULL,
	[LastUpdateUserId] [bigint] NOT NULL,
 CONSTRAINT [PK_ScanCodes] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ScanCodes] ADD  CONSTRAINT [DF_ScanCodes_ItemId]  DEFAULT ((0)) FOR [ItemId]
GO

ALTER TABLE [dbo].[ScanCodes] ADD  CONSTRAINT [DF_ScanCodes_ScanCode]  DEFAULT ('') FOR [ScanCode]
GO

ALTER TABLE [dbo].[ScanCodes] ADD  CONSTRAINT [DF_ScanCodes_DefaultFlg]  DEFAULT ((0)) FOR [TypicalFlg]
GO

ALTER TABLE [dbo].[ScanCodes] ADD  CONSTRAINT [DF_ScanCodes_NonPluFlg]  DEFAULT ((0)) FOR [NonPluFlg]
GO

ALTER TABLE [dbo].[ScanCodes] ADD  CONSTRAINT [DF_ScanCodes_DelFlg]  DEFAULT ((0)) FOR [DelFlg]
GO

ALTER TABLE [dbo].[ScanCodes] ADD  CONSTRAINT [DF_ScanCodes_CrDateTime]  DEFAULT (getdate()) FOR [CrDateTime]
GO

ALTER TABLE [dbo].[ScanCodes] ADD  CONSTRAINT [DF_ScanCodes_UpDateTime]  DEFAULT (getdate()) FOR [UpDateTime]
GO

ALTER TABLE [dbo].[ScanCodes] ADD  CONSTRAINT [DF_ScanCodes_LastUpdateUserId]  DEFAULT ((0)) FOR [LastUpdateUserId]
GO

ALTER TABLE [dbo].[ScanCodes]  WITH CHECK ADD  CONSTRAINT [FK_ScanCodes_Items] FOREIGN KEY([ItemId])
REFERENCES [dbo].[Items] ([Id])
GO

ALTER TABLE [dbo].[ScanCodes] CHECK CONSTRAINT [FK_ScanCodes_Items]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'商品Id' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ScanCodes', @level2type=N'COLUMN',@level2name=N'ItemId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'スキャンコード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ScanCodes', @level2type=N'COLUMN',@level2name=N'ScanCode'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'代表スキャンコードフラグ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ScanCodes', @level2type=N'COLUMN',@level2name=N'TypicalFlg'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'削除フラグ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ScanCodes', @level2type=N'COLUMN',@level2name=N'DelFlg'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'作成日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ScanCodes', @level2type=N'COLUMN',@level2name=N'CrDateTime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ScanCodes', @level2type=N'COLUMN',@level2name=N'UpDateTime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最終更新ユーザ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ScanCodes', @level2type=N'COLUMN',@level2name=N'LastUpdateUserId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'スキャンコード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ScanCodes'
GO


