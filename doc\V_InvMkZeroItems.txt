SELECT                      dbo.Stores.StoreCode, dbo.Items.ItemCode, '0' AS InvCount, dbo.V_InvUpdateStoreId.StoreId, 
                                      dbo.V_InvUpdateStoreId.InventoryDate AS InvDate, dbo.Items.Id AS ItemId
FROM                         dbo.ItemInventoriesQuant INNER JOIN
                                      dbo.Stores ON dbo.ItemInventoriesQuant.StoreId = dbo.Stores.Id INNER JOIN
                                      dbo.V_InvUpdateStoreId ON dbo.Stores.Id = dbo.V_InvUpdateStoreId.StoreId AND 
                                      dbo.ItemInventoriesQuant.StockDateTo >= dbo.V_InvUpdateStoreId.InventoryDate AND 
                                      dbo.ItemInventoriesQuant.StockDateFrom <= dbo.V_InvUpdateStoreId.InventoryDate LEFT OUTER JOIN
                                      dbo.ItemInventories_wk ON dbo.ItemInventoriesQuant.StoreId = dbo.ItemInventories_wk.StoreId AND 
                                      dbo.ItemInventoriesQuant.ItemId = dbo.ItemInventories_wk.ItemId LEFT OUTER JOIN
                                      dbo.Sections AS Sections_2 INNER JOIN
                                      dbo.Items INNER JOIN
                                      dbo.Sections ON dbo.Items.SectionId = dbo.Sections.Id INNER JOIN
                                      dbo.Sections AS Sections_1 ON dbo.Sections.ParentCodeId = Sections_1.Id ON 
                                      Sections_2.Id = Sections_1.ParentCodeId ON dbo.ItemInventoriesQuant.ItemId = dbo.Items.Id
WHERE                       (dbo.ItemInventoriesQuant.Quantity <> 0) AND (dbo.ItemInventories_wk.StoreId IS NULL) AND (dbo.Items.DelFlg = 0) 
                                      AND (Sections_2.CategoryCode = '531' OR
                                      Sections_2.CategoryCode = '411' OR
                                      Sections_2.CategoryCode = '511' OR
                                      Sections_2.CategoryCode = '801' OR
                                      Sections_2.CategoryCode = '932')
GROUP BY              dbo.ItemInventoriesQuant.ItemId, dbo.Items.ItemCode, dbo.Stores.StoreCode, dbo.V_InvUpdateStoreId.StoreId, 
                                      dbo.V_InvUpdateStoreId.InventoryDate, dbo.Items.Id