# データベース接続設定確認

## 🗄️ 接続情報

- **サーバー**: 192.7.49.31
- **データベース**: YsskKikan  
- **認証方式**: SQL Server認証
- **ユーザー**: sa
- **パスワード**: **********

## 📁 設定ファイル一覧

| ファイル名 | 環境 | 用途 |
|-----------|------|------|
| `appsettings.json` | 基本設定 | デフォルト環境 |
| `appsettings.Development.json` | 開発環境 | ローカル開発用（SQL Express） |
| `appsettings.Production.json` | 本番環境 | 実運用環境 |
| `appsettings.Staging.json` | ステージング | 開発者リモート接続用 |
| `appsettings.SqlAuth.json` | テンプレート | SQL認証設定例 |

## 🔌 接続文字列

```
Data Source=192.7.49.31;Initial Catalog=YsskKikan;User ID=sa;Password=**********;TrustServerCertificate=True;
```

## ⚠️ セキュリティ注意事項

1. **パスワード保護**: 本番環境では環境変数やUser Secretsの使用を推奨
2. **アクセス制限**: saアカウントは管理者権限のため、専用ユーザー作成を推奨
3. **接続暗号化**: TrustServerCertificate=Trueは開発用、本番では適切な証明書使用

## 🧪 接続テスト方法

### 1. SQL Server Management Studio (SSMS)
- サーバー: 192.7.49.31
- 認証: SQL Server認証
- ログイン: sa / **********

### 2. コマンドライン (sqlcmd)
```cmd
sqlcmd -S 192.7.49.31 -U sa -P ********** -d YsskKikan -Q "SELECT @@VERSION"
```

### 3. アプリケーション内テスト
プログラム起動時に接続テストを実行し、結果をログ出力する機能を実装予定。

## 🔧 トラブルシューティング

### エラー: サーバーへ接続できません
- ファイアウォール設定確認
- SQL Server Browserサービス確認
- ポート1433の開放確認

### エラー: ログインに失敗しました
- ユーザー名・パスワード確認
- SQL Server認証の有効化確認
- saアカウントの有効化確認 