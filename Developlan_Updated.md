# 棚卸結果CSV自動取込システム開発計画書

## 1. プロジェクト概要

### 目的
年2回実施される店舗別棚卸結果CSVファイルの取込処理をUIアプリケーションで効率化し、手動作業の省力化と処理の標準化を図る。

### 対象範囲
- 棚卸結果CSVファイルの選択・表示機能
- 複数店舗データの統合処理
- 統合ワークテーブルでのデータ管理
- データ確認・閲覧機能
- 基幹システムへの登録機能

## 2. 現在の実装状況（2025年1月30日時点）

### 2.1 完了済み機能 ✅
- **プロジェクト構成**: 3層アーキテクチャ（UI/Core/Data）の構築
- **設定管理**: appsettings.json による柔軟な設定管理
- **メインUI**: Windows Forms による基本UI実装（Form1.cs）
- **ファイル選択**: 店舗別CSVファイルの検索・選択機能
- **ファイル情報表示**: ファイル名、サイズ、更新日時、レコード数表示
- **UI基盤**: 進捗表示、エラーハンドリング基盤
- **FileDiscoveryService**: 店舗ディレクトリ検索とCSVファイル情報取得

### 2.2 実装中・未実装機能 🔄
- **DatabaseImportService**: データベース操作の中核サービス（未実装）
- **ImportProgress/ImportResult**: 進捗・結果報告モデル（未実装）
- **CSVインポート**: InventoryAll_wkテーブルへの実際のデータ投入
- **データ加工**: マスタ結合によるItemInventories_wk作成
- **データ確認**: ItemInventories_wkデータのグリッド表示
- **本番登録**: ItemInventoryテーブルへの最終登録

### 2.3 技術構成（確定）
- **プラットフォーム**: .NET 9.0 / C#
- **UI フレームワーク**: Windows Forms
- **データベース**: SQL Server（YsskKikanデータベース）
- **IDE**: Visual Studio Code + C# Dev Kit
- **パッケージ**: Microsoft.Data.SqlClient 6.1.0

### 2.4 プロジェクト構成（実装済み）
```
src/
├── InventoryImportSystem.sln              # ソリューションファイル
├── InventoryImportApp/                     # メインアプリケーション (Windows Forms)
│   ├── Config/                            # 設定ファイル
│   ├── Form1.cs                           # メインフォーム（実装済み）
│   └── InventoryImportApp.csproj
├── InventoryImportApp.Core/                # ビジネスロジック・共通ライブラリ
│   ├── Models/                            # モデルクラス
│   │   ├── AppSettings.cs                 # 設定クラス（実装済み）
│   │   └── InventoryFileInfo.cs           # ファイル情報クラス（実装済み）
│   ├── Services/                          # サービスクラス
│   │   ├── FileDiscoveryService.cs        # ファイル検索サービス（実装済み）
│   │   └── DatabaseImportService.cs       # DB操作サービス（未実装）
│   └── InventoryImportApp.Core.csproj
└── InventoryImportApp.Data/                # データアクセス層
    └── InventoryImportApp.Data.csproj
```

## 3. 次の開発ステップ

### 3.1 優先度1: DatabaseImportServiceの実装
**目的**: データベース操作の中核機能を実装

**実装すべきクラス・メソッド**:
```csharp
// 進捗報告用モデル
public class ImportProgress
{
    public string Phase { get; set; }
    public int ProgressPercentage { get; set; }
    public string CurrentFile { get; set; }
    public int ProcessedRecords { get; set; }
}

// インポート結果モデル
public class ImportResult
{
    public bool IsSuccess { get; set; }
    public string ErrorMessage { get; set; }
    public int ImportedCsvRecords { get; set; }
    public int ProcessedRecords { get; set; }
    public int RegisteredRecords { get; set; }
}

// ワークデータ統計モデル
public class WorkDataSummary
{
    public int TotalRecords { get; set; }
    public int StoreCount { get; set; }
    public int ItemCount { get; set; }
}

// データベース操作サービス
public class DatabaseImportService
{
    // 必要なメソッド
    Task<ImportResult> ImportSelectedFilesAsync(List<InventoryFileInfo> files, IProgress<ImportProgress> progress, DateTime registrationDate);
    Task ClearWorkTablesAsync();
    Task<List<object>> GetItemInventoriesWorkDataAsync();
    Task<WorkDataSummary> GetWorkDataSummaryAsync();
    Task<ImportResult> RegisterToProductionAsync(IProgress<ImportProgress> progress);
}
```

### 3.2 優先度2: テスト環境の構築
**目的**: 開発した機能の動作確認

**必要な作業**:
- テスト用CSVファイルの準備
- データベース接続テスト
- 基本的な単体テストの作成

### 3.3 優先度3: 後続処理の実装
**目的**: データ確認・本番登録機能の完成

**必要な作業**:
- データグリッド表示機能の改善
- 本番登録処理の実装
- エラーハンドリングの強化

## 4. 開発計画（今後2週間）

### Week 1: DatabaseImportService実装
- **Day 1-2**: ImportProgress, ImportResult, WorkDataSummaryモデルの実装
- **Day 3-4**: DatabaseImportServiceクラスの基本構造実装
- **Day 5**: CSVインポート機能の実装（InventoryAll_wkテーブル）

### Week 2: データ処理・テスト
- **Day 1-2**: マスタ結合処理の実装（ItemInventories_wk作成）
- **Day 3**: データ確認機能の実装
- **Day 4**: 本番登録機能の実装
- **Day 5**: 統合テスト・バグ修正

## 5. 技術的な検討事項

### 5.1 データベーステーブル構成
現在のコードから推測される構成:
- **InventoryAll_wk**: CSVデータの一時格納テーブル
- **ItemInventories_wk**: 加工済みデータのワークテーブル
- **ItemInventory**: 本番テーブル（最終登録先）

### 5.2 CSVファイル処理
- 文字エンコーディング: UTF-8, Shift_JIS対応
- 列構成: 店舗コード, 棚卸日, 商品コード, 在庫数
- エラーハンドリング: 不正データの検出・報告

### 5.3 パフォーマンス考慮事項
- 大量データ処理時のメモリ使用量最適化
- 非同期処理によるUI応答性確保
- プログレスバーによる進捗表示

## 6. リスク管理

### 6.1 技術的リスク
- **データベース接続**: 接続文字列の設定確認が必要
- **大量データ処理**: メモリ不足やタイムアウトの可能性
- **データ整合性**: マスタデータとの結合失敗

### 6.2 対策
- 段階的な実装とテスト
- 詳細なログ出力
- エラー時の安全な状態復旧

## 7. 成功基準

### 7.1 機能面
- CSVファイルの選択・インポートが正常に動作
- データ確認画面でワークデータが表示される
- 本番登録が正常に完了する

### 7.2 品質面
- エラー時に適切なメッセージが表示される
- 処理中の進捗が分かりやすく表示される
- 大量データでも安定して動作する

## 8. 次回のアクション

1. **DatabaseImportServiceクラスの作成**
2. **必要なモデルクラスの実装**
3. **基本的なデータベース接続テスト**
4. **CSVインポート機能の実装**
5. **統合テストの実行**

この計画に基づいて、段階的に機能を実装し、動作確認を行いながら開発を進めていきます。
