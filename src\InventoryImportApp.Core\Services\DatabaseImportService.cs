using Microsoft.Data.SqlClient;
using InventoryImportApp.Core.Models;
using System.Data;
using NLog;

namespace InventoryImportApp.Core.Services;

/// <summary>
/// データベースインポート処理を担当するサービスクラス
/// </summary>
public class DatabaseImportService
{
    private readonly string _connectionString;
    private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
    
    /// <summary>
    /// コンストラクタ
    /// </summary>
    /// <param name="connectionString">データベース接続文字列</param>
    public DatabaseImportService(string connectionString)
    {
        _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
    }
    
    /// <summary>
    /// 選択されたCSVファイルを一括インポートし、ItemInventories_wkテーブルまで処理する
    /// </summary>
    /// <param name="selectedFiles">選択されたCSVファイル一覧</param>
    /// <param name="progress">進捗報告用</param>
    /// <param name="registrationDate">登録日時</param>
    /// <returns>インポート結果</returns>
    public async Task<ImportResult> ImportSelectedFilesAsync(
        List<InventoryFileInfo> selectedFiles, 
        IProgress<ImportProgress> progress, 
        DateTime registrationDate)
    {
        var result = new ImportResult
        {
            StartTime = DateTime.Now
        };

        _logger.Info("=== インポート処理開始 ===");
        _logger.Info($"選択ファイル数: {selectedFiles.Count}件");
        _logger.Info($"登録日時: {registrationDate:yyyy-MM-dd HH:mm:ss}");

        foreach (var file in selectedFiles)
        {
            _logger.Info($"対象ファイル: {file.FileName} ({file.RecordCount:N0}件)");
        }

        try
        {
            // フェーズ1: ワークテーブルクリア
            _logger.Info("フェーズ1: ワークテーブル初期化開始");
            progress?.Report(new ImportProgress
            {
                Phase = "ワークテーブル初期化中...",
                ProgressPercentage = 5
            });
            await ClearWorkTablesAsync();
            _logger.Info("フェーズ1: ワークテーブル初期化完了");

            // フェーズ2: CSVファイルインポート
            _logger.Info("フェーズ2: CSVファイルインポート開始");
            progress?.Report(new ImportProgress
            {
                Phase = "CSVファイル読込中...",
                ProgressPercentage = 10
            });

            try
            {
                result.ImportedCsvRecords = await ImportCsvFilesToInventoryAllWorkAsync(selectedFiles, progress);
                _logger.Info($"フェーズ2: CSVファイルインポート完了 - {result.ImportedCsvRecords:N0}件");
                progress?.Report(new ImportProgress
                {
                    Phase = "CSVファイル読込完了",
                    ProgressPercentage = 25,
                    ProcessedRecords = result.ImportedCsvRecords
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "CSVファイルインポート中にエラーが発生");
                throw new InvalidOperationException($"CSVファイルインポート中にエラーが発生しました: {ex.Message}", ex);
            }
            
            // フェーズ3: 基本マスタ結合処理
            _logger.Info("フェーズ3: 基本マスタ結合処理開始");
            progress?.Report(new ImportProgress
            {
                Phase = "マスタ結合処理中...",
                ProgressPercentage = 30
            });
            await ProcessBasicMasterJoinAsync(registrationDate, progress);
            _logger.Info("フェーズ3: 基本マスタ結合処理完了");
            
            // フェーズ4: SectionId更新
            progress?.Report(new ImportProgress 
            { 
                Phase = "部門情報設定中...", 
                ProgressPercentage = 50 
            });
            await UpdateSectionInfoAsync(progress);
            
            // フェーズ5: 在庫0商品補完
            progress?.Report(new ImportProgress 
            { 
                Phase = "在庫0商品補完中...", 
                ProgressPercentage = 65 
            });
            result.ZeroInventoryRecords = await ProcessZeroInventoryItemsAsync(registrationDate, progress);
            
            // フェーズ6: 売価情報更新
            progress?.Report(new ImportProgress 
            { 
                Phase = "売価情報更新中...", 
                ProgressPercentage = 80 
            });
            await UpdatePriceInfoAsync(progress);
            
            // フェーズ7: 重複データ削除
            progress?.Report(new ImportProgress 
            { 
                Phase = "重複データ削除中...", 
                ProgressPercentage = 90 
            });
            result.DuplicateRemovedRecords = await RemoveDuplicateDataAsync(progress);
            
            // 最終的な処理レコード数を取得
            result.ProcessedRecords = await GetProcessedRecordCountAsync();
            
            progress?.Report(new ImportProgress 
            { 
                Phase = "インポート処理完了", 
                ProgressPercentage = 100 
            });
            
            result.IsSuccess = true;
            result.EndTime = DateTime.Now;

            _logger.Info("=== インポート処理完了 ===");
            _logger.Info($"処理時間: {result.Duration.TotalSeconds:F1}秒");
            _logger.Info($"CSVレコード数: {result.ImportedCsvRecords:N0}件");
            _logger.Info($"処理レコード数: {result.ProcessedRecords:N0}件");
            _logger.Info($"在庫0補完数: {result.ZeroInventoryRecords:N0}件");
            _logger.Info($"重複削除数: {result.DuplicateRemovedRecords:N0}件");

            return result;
        }
        catch (Exception ex)
        {
            result.IsSuccess = false;
            result.ErrorMessage = ex.Message;
            result.EndTime = DateTime.Now;

            _logger.Error(ex, "インポート処理中に致命的なエラーが発生");
            _logger.Info($"処理時間: {result.Duration.TotalSeconds:F1}秒（エラー終了）");

            return result;
        }
    }
    
    /// <summary>
    /// ワークテーブルをクリアする
    /// </summary>
    public async Task ClearWorkTablesAsync()
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var transaction = connection.BeginTransaction();
            try
            {
                // ItemInventories_wkテーブルクリア
                using var cmd1 = new SqlCommand("TRUNCATE TABLE dbo.ItemInventories_wk", connection, transaction);
                await cmd1.ExecuteNonQueryAsync();

                // InventoryAll_wkテーブルクリア
                using var cmd2 = new SqlCommand("TRUNCATE TABLE dbo.InventoryAll_wk", connection, transaction);
                await cmd2.ExecuteNonQueryAsync();

                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"ワークテーブルクリア中にエラーが発生しました: {ex.Message}", ex);
        }
    }
    
    /// <summary>
    /// ItemInventories_wkのデータを取得する（データ確認用）
    /// </summary>
    public async Task<List<object>> GetItemInventoriesWorkDataAsync()
    {
        try
        {
            _logger.Info("データ確認用のワークデータ取得開始");

            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

        const string sql = @"
            SELECT
                wk.Id,
                wk.InventoryDate,
                s.StoreName,
                s.StoreCode,
                i.ItemName,
                i.ItemCode,
                sec.CategoryName as SectionName,
                wk.InventoryCount,
                wk.UnitPrice,
                wk.Price,
                wk.CrDateTime
            FROM dbo.ItemInventories_wk wk
            INNER JOIN dbo.Stores s ON wk.StoreId = s.Id
            INNER JOIN dbo.Items i ON wk.ItemId = i.Id
            LEFT JOIN dbo.Sections sec ON wk.SectionId = sec.Id AND sec.DelFlg = 0
            ORDER BY wk.Id";

            var results = new List<object>();

            using var cmd = new SqlCommand(sql, connection);
            using var reader = await cmd.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                results.Add(new
                {
                    Id = reader.GetInt64("Id"),
                    InventoryDate = reader.GetDateTime("InventoryDate"),
                    StoreName = reader.IsDBNull("StoreName") ? "" : reader.GetString("StoreName"),
                    StoreCode = reader.IsDBNull("StoreCode") ? "" : reader.GetString("StoreCode"),
                    ItemName = reader.IsDBNull("ItemName") ? "" : reader.GetString("ItemName"),
                    ItemCode = reader.IsDBNull("ItemCode") ? "" : reader.GetString("ItemCode"),
                    SectionName = reader.IsDBNull("SectionName") ? "" : reader.GetString("SectionName"),
                    InventoryCount = reader.IsDBNull("InventoryCount") ? 0 : reader.GetInt32("InventoryCount"),
                    UnitPrice = reader.IsDBNull("UnitPrice") ? 0 : reader.GetInt32("UnitPrice"),
                    Price = reader.IsDBNull("Price") ? 0 : reader.GetInt32("Price"),
                    CrDateTime = reader.GetDateTime("CrDateTime")
                });
            }

            _logger.Info($"データ確認用のワークデータ取得完了: {results.Count:N0}件（全件表示）");
            return results;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "データ確認用のワークデータ取得中にエラーが発生");
            throw new InvalidOperationException($"データ確認用のワークデータ取得中にエラーが発生しました: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// ItemInventories_wkの統計情報を取得する
    /// </summary>
    public async Task<WorkDataSummary> GetWorkDataSummaryAsync()
    {
        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        const string sql = @"
            SELECT
                COUNT(*) as TotalRecords,
                COUNT(DISTINCT StoreId) as StoreCount,
                COUNT(DISTINCT ItemId) as ItemCount,
                COUNT(DISTINCT SectionId) as SectionCount,
                MIN(InventoryDate) as MinInventoryDate,
                MAX(InventoryDate) as MaxInventoryDate,
                SUM(CAST(InventoryCount as bigint)) as TotalInventoryCount,
                SUM(CASE WHEN InventoryCount = 0 THEN 1 ELSE 0 END) as ZeroInventoryItemCount,
                MIN(InventoryCount) as MinInventoryCount,
                MAX(InventoryCount) as MaxInventoryCount,
                MIN(CrDateTime) as MinCreateDateTime,
                MAX(CrDateTime) as MaxCreateDateTime
            FROM dbo.ItemInventories_wk";

        using var cmd = new SqlCommand(sql, connection);
        using var reader = await cmd.ExecuteReaderAsync();

        if (await reader.ReadAsync())
        {
            return new WorkDataSummary
            {
                TotalRecords = reader.GetInt32("TotalRecords"),
                StoreCount = reader.GetInt32("StoreCount"),
                ItemCount = reader.GetInt32("ItemCount"),
                SectionCount = reader.GetInt32("SectionCount"),
                InventoryDate = reader.IsDBNull("MinInventoryDate") ? null : reader.GetDateTime("MinInventoryDate"),
                TotalInventoryCount = reader.IsDBNull("TotalInventoryCount") ? 0 : reader.GetInt64("TotalInventoryCount"),
                ZeroInventoryItemCount = reader.GetInt32("ZeroInventoryItemCount"),
                MinInventoryCount = reader.GetInt32("MinInventoryCount"),
                MaxInventoryCount = reader.GetInt32("MaxInventoryCount"),
                MinCreateDateTime = reader.IsDBNull("MinCreateDateTime") ? null : reader.GetDateTime("MinCreateDateTime"),
                MaxCreateDateTime = reader.IsDBNull("MaxCreateDateTime") ? null : reader.GetDateTime("MaxCreateDateTime")
            };
        }

        return new WorkDataSummary();
    }
    
    /// <summary>
    /// 本番登録処理（未実装）
    /// </summary>
    public async Task<ImportResult> RegisterToProductionAsync(IProgress<ImportProgress> progress)
    {
        // TODO: 本番登録処理は後で実装
        await Task.Delay(1000);
        return ImportResult.Success(0, 0);
    }
    
    /// <summary>
    /// 選択されたCSVファイルをInventoryAll_wkテーブルに投入する
    /// </summary>
    private async Task<int> ImportCsvFilesToInventoryAllWorkAsync(List<InventoryFileInfo> selectedFiles, IProgress<ImportProgress> progress)
    {
        var totalRecords = 0;
        var processedFiles = 0;

        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        foreach (var fileInfo in selectedFiles)
        {
            progress?.Report(new ImportProgress
            {
                Phase = $"CSVファイル読込中: {fileInfo.FileName}",
                ProgressPercentage = 10 + (processedFiles * 15 / selectedFiles.Count),
                CurrentFile = fileInfo.FileName,
                ProcessedRecords = totalRecords
            });

            try
            {
                var fileRecords = await ImportSingleCsvFileAsync(connection, fileInfo);
                totalRecords += fileRecords;
                processedFiles++;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"ファイル '{fileInfo.FileName}' の処理中にエラーが発生しました: {ex.Message}", ex);
            }
        }

        return totalRecords;
    }

    /// <summary>
    /// 単一のCSVファイルをInventoryAll_wkテーブルに投入する（SqlBulkCopy使用）
    /// </summary>
    private async Task<int> ImportSingleCsvFileAsync(SqlConnection connection, InventoryFileInfo fileInfo)
    {
        try
        {
            // CSVファイルを読み込み
            if (!File.Exists(fileInfo.FilePath))
            {
                throw new FileNotFoundException($"CSVファイルが見つかりません: {fileInfo.FilePath}");
            }

            var lines = await File.ReadAllLinesAsync(fileInfo.FilePath);

            if (lines.Length <= 1)
            {
                throw new InvalidOperationException($"CSVファイルにデータがありません: {fileInfo.FileName}");
            }

            _logger.Info($"CSVファイル読み込み開始: {fileInfo.FileName} ({lines.Length - 1:N0}行)");

            // DataTableを作成してBulkCopyで高速インポート
            var dataTable = new DataTable();
            dataTable.Columns.Add("列 0", typeof(string));
            dataTable.Columns.Add("列 1", typeof(string));
            dataTable.Columns.Add("列 2", typeof(string));
            dataTable.Columns.Add("列 3", typeof(string));

            // ヘッダー行をスキップしてDataTableに追加
            for (int i = 1; i < lines.Length; i++)
            {
                var line = lines[i].Trim();
                if (string.IsNullOrEmpty(line)) continue;

                try
                {
                    // CSVの各列を解析（カンマ区切り、ダブルクォート対応）
                    var columns = ParseCsvLine(line);
                    if (columns.Length >= 4)
                    {
                        var row = dataTable.NewRow();
                        row["列 0"] = columns[0];
                        row["列 1"] = columns[1];
                        row["列 2"] = columns[2];
                        row["列 3"] = columns[3];
                        dataTable.Rows.Add(row);
                    }
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"CSV行の解析エラー (行{i + 1}): {ex.Message}", ex);
                }
            }

            _logger.Info($"DataTable作成完了: {dataTable.Rows.Count:N0}行");

            // SqlBulkCopyで高速インポート
            using var bulkCopy = new SqlBulkCopy(connection);
            bulkCopy.DestinationTableName = "dbo.InventoryAll_wk";
            bulkCopy.BatchSize = 10000; // 大きなバッチサイズで高速化
            bulkCopy.BulkCopyTimeout = 300; // 5分のタイムアウト

            // 列マッピング
            bulkCopy.ColumnMappings.Add("列 0", "列 0");
            bulkCopy.ColumnMappings.Add("列 1", "列 1");
            bulkCopy.ColumnMappings.Add("列 2", "列 2");
            bulkCopy.ColumnMappings.Add("列 3", "列 3");

            await bulkCopy.WriteToServerAsync(dataTable);

            _logger.Info($"BulkCopy完了: {dataTable.Rows.Count:N0}行");
            return dataTable.Rows.Count;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"CSVファイル処理中にエラーが発生しました ({fileInfo.FileName}): {ex.Message}", ex);
        }
    }

    /// <summary>
    /// CSV行を解析する（ダブルクォート対応）
    /// </summary>
    private string[] ParseCsvLine(string line)
    {
        var columns = new List<string>();
        var currentColumn = "";
        var inQuotes = false;

        for (int i = 0; i < line.Length; i++)
        {
            var c = line[i];

            if (c == '"')
            {
                inQuotes = !inQuotes;
            }
            else if (c == ',' && !inQuotes)
            {
                columns.Add(currentColumn.Trim('"'));
                currentColumn = "";
            }
            else
            {
                currentColumn += c;
            }
        }

        // 最後の列を追加
        columns.Add(currentColumn.Trim('"'));

        return columns.ToArray();
    }

    // 旧バッチ挿入メソッドは削除（SqlBulkCopyに置き換え）
    
    /// <summary>
    /// InventoryAll_wkからItemInventories_wkへの基本的なマスタ結合処理
    /// パフォーマンス向上のため、複雑なビューを使わずシンプルなJOINで実装
    /// </summary>
    private async Task ProcessBasicMasterJoinAsync(DateTime registrationDate, IProgress<ImportProgress> progress)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // まず、データの存在確認
            var inventoryCount = await GetInventoryAllWorkCountAsync(connection);
            _logger.Info($"InventoryAll_wkレコード数: {inventoryCount:N0}件");
            if (inventoryCount == 0)
            {
                throw new InvalidOperationException("InventoryAll_wkテーブルにデータがありません");
            }

            // マスタテーブルの存在確認
            var storeCount = await GetTableRecordCountAsync(connection, "Stores", "DelFlg = 0");
            var itemCount = await GetTableRecordCountAsync(connection, "Items", "DelFlg = 0");
            _logger.Info($"アクティブ店舗数: {storeCount:N0}件");
            _logger.Info($"アクティブ商品数: {itemCount:N0}件");

            if (storeCount == 0)
            {
                throw new InvalidOperationException("Storesテーブルにアクティブなデータがありません");
            }

            if (itemCount == 0)
            {
                throw new InvalidOperationException("Itemsテーブルにアクティブなデータがありません");
            }

            // マスタ結合可能なレコード数を事前確認
            var matchableRecords = await GetMatchableRecordsCountAsync(connection);
            _logger.Info($"マスタ結合可能レコード数: {matchableRecords:N0}件");

            progress?.Report(new ImportProgress
            {
                Phase = $"マスタ結合処理中... (CSV:{inventoryCount:N0}件, 店舗:{storeCount:N0}件, 商品:{itemCount:N0}件, 結合可能:{matchableRecords:N0}件)",
                ProgressPercentage = 32,
                ProcessedRecords = inventoryCount,
                DetailMessage = matchableRecords == 0 ? "警告: マスタと結合可能なレコードがありません" : ""
            });

            // 基本的なマスタ結合のみ実行（SectionIdは後で別途更新）
            const string sql = @"
                INSERT INTO dbo.ItemInventories_wk
                    (StoreId, ItemId, InventoryCount, SectionId, LastUpdateUserId, DelFlg, InventoryDate, CrDateTime, UpDateTime)
                SELECT
                    s.Id as StoreId,
                    i.Id as ItemId,
                    CAST(w.[列 3] as int) as InventoryCount,
                    ISNULL(i.SectionId, 1) as SectionId,  -- SectionIdがNULLの場合は1を設定
                    12 as LastUpdateUserId,
                    0 as DelFlg,
                    CAST(w.[列 1] as date) as InventoryDate,
                    @RegistrationDate as CrDateTime,
                    @RegistrationDate as UpDateTime
                FROM dbo.InventoryAll_wk w
                INNER JOIN dbo.Stores s ON w.[列 0] = s.StoreCode AND s.DelFlg = 0
                INNER JOIN dbo.Items i ON w.[列 2] = i.ItemCode AND i.DelFlg = 0
                WHERE w.[列 0] IS NOT NULL
                  AND w.[列 1] IS NOT NULL
                  AND w.[列 2] IS NOT NULL
                  AND w.[列 3] IS NOT NULL
                  AND ISNUMERIC(w.[列 3]) = 1
                  AND TRY_CAST(w.[列 1] as date) IS NOT NULL";

        using var transaction = connection.BeginTransaction();
        try
        {
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@RegistrationDate", registrationDate);
            cmd.CommandTimeout = 300; // 5分のタイムアウト

            var processedRecords = await cmd.ExecuteNonQueryAsync();
            _logger.Info($"マスタ結合SQL実行結果: {processedRecords:N0}件処理");

            await transaction.CommitAsync();

            // 結果の詳細確認
            var resultSummary = await GetMasterJoinResultSummaryAsync(connection);
            _logger.Info($"マスタ結合後統計 - 在庫合計: {resultSummary.TotalInventory:N0}, 平均在庫: {resultSummary.AvgInventory:F1}");

            progress?.Report(new ImportProgress
            {
                Phase = "マスタ結合処理完了",
                ProgressPercentage = 40,
                ProcessedRecords = processedRecords,
                DetailMessage = $"マスタ結合: {processedRecords:N0}件処理, 在庫合計: {resultSummary.TotalInventory:N0}, 平均在庫: {resultSummary.AvgInventory:F1}"
            });
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            throw new InvalidOperationException($"マスタ結合処理中にエラーが発生しました: {ex.Message}", ex);
        }
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"マスタ結合処理の初期化中にエラーが発生しました: {ex.Message}", ex);
        }
    }
    
    /// <summary>
    /// SectionId情報を更新する
    /// 元のV_InvUpdate_Workビューの3層Sections結合を簡素化
    /// </summary>
    private async Task UpdateSectionInfoAsync(IProgress<ImportProgress> progress)
    {
        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        // 基本的なマスタ結合で既にSectionIdは設定済みなので、
        // 必要に応じて階層的なSectionId（親部門）への更新を行う
        // 現在は元のItemsテーブルのSectionIdをそのまま使用

        // 将来的に階層的なSection処理が必要な場合は以下のようなSQLを実行
        /*
        const string sql = @"
            UPDATE wk
            SET SectionId = s2.Id
            FROM dbo.ItemInventories_wk wk
            INNER JOIN dbo.Items i ON wk.ItemId = i.Id
            INNER JOIN dbo.Sections s ON i.SectionId = s.Id
            INNER JOIN dbo.Sections s1 ON s.ParentCodeId = s1.Id
            INNER JOIN dbo.Sections s2 ON s1.ParentCodeId = s2.Id
            WHERE s2.Id IS NOT NULL";
        */

        // 現在は何もしない（基本マスタ結合で十分）
        await Task.Delay(50);

        progress?.Report(new ImportProgress
        {
            Phase = "部門情報設定完了",
            ProgressPercentage = 55,
            DetailMessage = "部門情報の設定が完了しました"
        });
    }
    
    /// <summary>
    /// 在庫0商品補完処理
    /// ItemInventoriesQuantテーブルを参照して、棚卸データにない商品を在庫0として追加
    /// 元のV_InvZeroUpdate_Workビューの処理を簡素化
    /// </summary>
    private async Task<int> ProcessZeroInventoryItemsAsync(DateTime registrationDate, IProgress<ImportProgress> progress)
    {
        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        // 棚卸日を取得（ItemInventories_wkから）
        var inventoryDate = await GetInventoryDateFromWorkTableAsync(connection);
        if (!inventoryDate.HasValue)
        {
            progress?.Report(new ImportProgress
            {
                Phase = "在庫0商品補完スキップ",
                ProgressPercentage = 70,
                DetailMessage = "棚卸日が取得できないため、在庫0商品補完をスキップしました"
            });
            return 0;
        }

        // 在庫0商品を追加するSQL（簡素化版）
        const string sql = @"
            INSERT INTO dbo.ItemInventories_wk
                (StoreId, ItemId, SectionId, LastUpdateUserId, DelFlg, CrDateTime, UpDateTime, InventoryDate, InventoryCount)
            SELECT DISTINCT
                q.StoreId,
                q.ItemId,
                i.SectionId,
                12 as LastUpdateUserId,
                0 as DelFlg,
                @RegistrationDate as CrDateTime,
                @RegistrationDate as UpDateTime,
                @InventoryDate as InventoryDate,
                0 as InventoryCount
            FROM dbo.ItemInventoriesQuant q
            INNER JOIN dbo.Items i ON q.ItemId = i.Id
            INNER JOIN dbo.Sections s ON i.SectionId = s.Id
            LEFT JOIN dbo.ItemInventories_wk wk ON q.StoreId = wk.StoreId AND q.ItemId = wk.ItemId
            WHERE q.Quantity <> 0
              AND wk.StoreId IS NULL
              AND i.DelFlg = 0
              AND q.StockDateFrom <= @InventoryDate
              AND q.StockDateTo >= @InventoryDate
              AND EXISTS (SELECT 1 FROM dbo.ItemInventories_wk wk2 WHERE wk2.StoreId = q.StoreId)";

        using var transaction = connection.BeginTransaction();
        try
        {
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@RegistrationDate", registrationDate);
            cmd.Parameters.AddWithValue("@InventoryDate", inventoryDate.Value);
            cmd.CommandTimeout = 300; // 5分のタイムアウト

            var addedRecords = await cmd.ExecuteNonQueryAsync();

            await transaction.CommitAsync();

            progress?.Report(new ImportProgress
            {
                Phase = "在庫0商品補完完了",
                ProgressPercentage = 75,
                ProcessedRecords = addedRecords,
                DetailMessage = $"{addedRecords:N0}件の在庫0商品を追加しました"
            });

            return addedRecords;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    /// <summary>
    /// ItemInventories_wkテーブルから棚卸日を取得する
    /// </summary>
    private async Task<DateTime?> GetInventoryDateFromWorkTableAsync(SqlConnection connection)
    {
        const string sql = "SELECT TOP 1 InventoryDate FROM dbo.ItemInventories_wk";

        using var cmd = new SqlCommand(sql, connection);
        var result = await cmd.ExecuteScalarAsync();

        return result as DateTime?;
    }
    
    /// <summary>
    /// 売価情報更新処理
    /// Itemsテーブルから売価情報を取得してItemInventories_wkを更新
    /// </summary>
    private async Task UpdatePriceInfoAsync(IProgress<ImportProgress> progress)
    {
        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        const string sql = @"
            UPDATE wk
            SET
                UnitPrice = i.RegularPrice,
                Price = i.RegularPrice * wk.InventoryCount,
                UnitPriceTaxIn = i.RegularPriceTaxIn,
                PriceTaxIn = i.RegularPriceTaxIn * wk.InventoryCount
            FROM dbo.ItemInventories_wk wk
            INNER JOIN dbo.Items i ON wk.ItemId = i.Id
            WHERE i.RegularPrice IS NOT NULL";

        using var transaction = connection.BeginTransaction();
        try
        {
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.CommandTimeout = 300; // 5分のタイムアウト

            var updatedRecords = await cmd.ExecuteNonQueryAsync();

            await transaction.CommitAsync();

            progress?.Report(new ImportProgress
            {
                Phase = "売価情報更新完了",
                ProgressPercentage = 85,
                ProcessedRecords = updatedRecords,
                DetailMessage = $"{updatedRecords:N0}件のレコードの売価情報を更新しました"
            });
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }
    
    /// <summary>
    /// 重複データ削除処理
    /// 定期棚卸日以降に店舗が棚卸したデータがあれば、ItemInventories_wkから削除
    /// memo.txtの⑧番の処理に相当
    /// </summary>
    private async Task<int> RemoveDuplicateDataAsync(IProgress<ImportProgress> progress)
    {
        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        const string sql = @"
            DELETE wk
            FROM dbo.ItemInventories_wk wk
            INNER JOIN dbo.ItemInventories ii ON
                wk.ItemId = ii.ItemId
                AND wk.StoreId = ii.StoreId
                AND wk.InventoryDate <= ii.InventoryDate";

        using var transaction = connection.BeginTransaction();
        try
        {
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.CommandTimeout = 300; // 5分のタイムアウト

            var deletedRecords = await cmd.ExecuteNonQueryAsync();

            await transaction.CommitAsync();

            progress?.Report(new ImportProgress
            {
                Phase = "重複データ削除完了",
                ProgressPercentage = 95,
                ProcessedRecords = deletedRecords,
                DetailMessage = $"{deletedRecords:N0}件の重複レコードを削除しました"
            });

            return deletedRecords;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }
    
    private async Task<int> GetProcessedRecordCountAsync()
    {
        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        using var cmd = new SqlCommand("SELECT COUNT(*) FROM dbo.ItemInventories_wk", connection);
        var result = await cmd.ExecuteScalarAsync();
        return Convert.ToInt32(result);
    }

    /// <summary>
    /// InventoryAll_wkテーブルのレコード数を取得
    /// </summary>
    private async Task<int> GetInventoryAllWorkCountAsync(SqlConnection connection)
    {
        using var cmd = new SqlCommand("SELECT COUNT(*) FROM dbo.InventoryAll_wk", connection);
        var result = await cmd.ExecuteScalarAsync();
        return Convert.ToInt32(result);
    }

    /// <summary>
    /// 指定されたテーブルのレコード数を取得
    /// </summary>
    private async Task<int> GetTableRecordCountAsync(SqlConnection connection, string tableName, string whereClause = "")
    {
        var sql = $"SELECT COUNT(*) FROM dbo.{tableName}";
        if (!string.IsNullOrEmpty(whereClause))
        {
            sql += $" WHERE {whereClause}";
        }

        using var cmd = new SqlCommand(sql, connection);
        var result = await cmd.ExecuteScalarAsync();
        return Convert.ToInt32(result);
    }

    /// <summary>
    /// マスタと結合可能なレコード数を取得
    /// </summary>
    private async Task<int> GetMatchableRecordsCountAsync(SqlConnection connection)
    {
        const string sql = @"
            SELECT COUNT(*)
            FROM dbo.InventoryAll_wk w
            INNER JOIN dbo.Stores s ON w.[列 0] = s.StoreCode AND s.DelFlg = 0
            INNER JOIN dbo.Items i ON w.[列 2] = i.ItemCode AND i.DelFlg = 0
            WHERE w.[列 0] IS NOT NULL
              AND w.[列 1] IS NOT NULL
              AND w.[列 2] IS NOT NULL
              AND w.[列 3] IS NOT NULL
              AND ISNUMERIC(w.[列 3]) = 1
              AND TRY_CAST(w.[列 1] as date) IS NOT NULL";

        using var cmd = new SqlCommand(sql, connection);
        var result = await cmd.ExecuteScalarAsync();
        return Convert.ToInt32(result);
    }

    /// <summary>
    /// マスタ結合結果の統計情報を取得
    /// </summary>
    private async Task<(long TotalInventory, double AvgInventory)> GetMasterJoinResultSummaryAsync(SqlConnection connection)
    {
        const string sql = @"
            SELECT
                SUM(CAST(InventoryCount as bigint)) as TotalInventory,
                AVG(CAST(InventoryCount as float)) as AvgInventory
            FROM dbo.ItemInventories_wk
            WHERE InventoryCount > 0";

        using var cmd = new SqlCommand(sql, connection);
        using var reader = await cmd.ExecuteReaderAsync();

        if (await reader.ReadAsync())
        {
            var totalInventory = reader.IsDBNull("TotalInventory") ? 0L : reader.GetInt64("TotalInventory");
            var avgInventory = reader.IsDBNull("AvgInventory") ? 0.0 : reader.GetDouble("AvgInventory");
            return (totalInventory, avgInventory);
        }

        return (0L, 0.0);
    }
}
