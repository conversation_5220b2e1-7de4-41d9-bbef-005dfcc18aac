using Microsoft.Data.SqlClient;
using InventoryImportApp.Core.Models;
using System.Data;

namespace InventoryImportApp.Core.Services;

/// <summary>
/// データベースインポート処理を担当するサービスクラス
/// </summary>
public class DatabaseImportService
{
    private readonly string _connectionString;
    
    /// <summary>
    /// コンストラクタ
    /// </summary>
    /// <param name="connectionString">データベース接続文字列</param>
    public DatabaseImportService(string connectionString)
    {
        _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
    }
    
    /// <summary>
    /// 選択されたCSVファイルを一括インポートし、ItemInventories_wkテーブルまで処理する
    /// </summary>
    /// <param name="selectedFiles">選択されたCSVファイル一覧</param>
    /// <param name="progress">進捗報告用</param>
    /// <param name="registrationDate">登録日時</param>
    /// <returns>インポート結果</returns>
    public async Task<ImportResult> ImportSelectedFilesAsync(
        List<InventoryFileInfo> selectedFiles, 
        IProgress<ImportProgress> progress, 
        DateTime registrationDate)
    {
        var result = new ImportResult
        {
            StartTime = DateTime.Now
        };
        
        try
        {
            // フェーズ1: ワークテーブルクリア
            progress?.Report(new ImportProgress
            {
                Phase = "ワークテーブル初期化中...",
                ProgressPercentage = 5
            });
            await ClearWorkTablesAsync();

            // フェーズ2: CSVファイルインポート
            progress?.Report(new ImportProgress
            {
                Phase = "CSVファイル読込中...",
                ProgressPercentage = 10
            });

            try
            {
                result.ImportedCsvRecords = await ImportCsvFilesToInventoryAllWorkAsync(selectedFiles, progress);
                progress?.Report(new ImportProgress
                {
                    Phase = "CSVファイル読込完了",
                    ProgressPercentage = 25,
                    ProcessedRecords = result.ImportedCsvRecords
                });
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"CSVファイルインポート中にエラーが発生しました: {ex.Message}", ex);
            }
            
            // フェーズ3: 基本マスタ結合処理
            progress?.Report(new ImportProgress 
            { 
                Phase = "マスタ結合処理中...", 
                ProgressPercentage = 30 
            });
            await ProcessBasicMasterJoinAsync(registrationDate, progress);
            
            // フェーズ4: SectionId更新
            progress?.Report(new ImportProgress 
            { 
                Phase = "部門情報設定中...", 
                ProgressPercentage = 50 
            });
            await UpdateSectionInfoAsync(progress);
            
            // フェーズ5: 在庫0商品補完
            progress?.Report(new ImportProgress 
            { 
                Phase = "在庫0商品補完中...", 
                ProgressPercentage = 65 
            });
            result.ZeroInventoryRecords = await ProcessZeroInventoryItemsAsync(registrationDate, progress);
            
            // フェーズ6: 売価情報更新
            progress?.Report(new ImportProgress 
            { 
                Phase = "売価情報更新中...", 
                ProgressPercentage = 80 
            });
            await UpdatePriceInfoAsync(progress);
            
            // フェーズ7: 重複データ削除
            progress?.Report(new ImportProgress 
            { 
                Phase = "重複データ削除中...", 
                ProgressPercentage = 90 
            });
            result.DuplicateRemovedRecords = await RemoveDuplicateDataAsync(progress);
            
            // 最終的な処理レコード数を取得
            result.ProcessedRecords = await GetProcessedRecordCountAsync();
            
            progress?.Report(new ImportProgress 
            { 
                Phase = "インポート処理完了", 
                ProgressPercentage = 100 
            });
            
            result.IsSuccess = true;
            result.EndTime = DateTime.Now;
            
            return result;
        }
        catch (Exception ex)
        {
            result.IsSuccess = false;
            result.ErrorMessage = ex.Message;
            result.EndTime = DateTime.Now;
            
            return result;
        }
    }
    
    /// <summary>
    /// ワークテーブルをクリアする
    /// </summary>
    public async Task ClearWorkTablesAsync()
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var transaction = connection.BeginTransaction();
            try
            {
                // ItemInventories_wkテーブルクリア
                using var cmd1 = new SqlCommand("TRUNCATE TABLE dbo.ItemInventories_wk", connection, transaction);
                await cmd1.ExecuteNonQueryAsync();

                // InventoryAll_wkテーブルクリア
                using var cmd2 = new SqlCommand("TRUNCATE TABLE dbo.InventoryAll_wk", connection, transaction);
                await cmd2.ExecuteNonQueryAsync();

                await transaction.CommitAsync();
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"ワークテーブルクリア中にエラーが発生しました: {ex.Message}", ex);
        }
    }
    
    /// <summary>
    /// ItemInventories_wkのデータを取得する（データ確認用）
    /// </summary>
    public async Task<List<object>> GetItemInventoriesWorkDataAsync()
    {
        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        const string sql = @"
            SELECT TOP 1000
                wk.Id,
                wk.InventoryDate,
                s.StoreName,
                s.StoreCode,
                i.ItemName,
                i.ItemCode,
                sec.SectionName,
                wk.InventoryCount,
                wk.UnitPrice,
                wk.Price,
                wk.CrDateTime
            FROM dbo.ItemInventories_wk wk
            INNER JOIN dbo.Stores s ON wk.StoreId = s.Id
            INNER JOIN dbo.Items i ON wk.ItemId = i.Id
            LEFT JOIN dbo.Sections sec ON wk.SectionId = sec.Id
            ORDER BY wk.Id";

        var results = new List<object>();

        using var cmd = new SqlCommand(sql, connection);
        using var reader = await cmd.ExecuteReaderAsync();

        while (await reader.ReadAsync())
        {
            results.Add(new
            {
                Id = reader.GetInt64("Id"),
                InventoryDate = reader.GetDateTime("InventoryDate"),
                StoreName = reader.IsDBNull("StoreName") ? "" : reader.GetString("StoreName"),
                StoreCode = reader.IsDBNull("StoreCode") ? "" : reader.GetString("StoreCode"),
                ItemName = reader.IsDBNull("ItemName") ? "" : reader.GetString("ItemName"),
                ItemCode = reader.IsDBNull("ItemCode") ? "" : reader.GetString("ItemCode"),
                SectionName = reader.IsDBNull("SectionName") ? "" : reader.GetString("SectionName"),
                InventoryCount = reader.IsDBNull("InventoryCount") ? 0 : reader.GetInt32("InventoryCount"),
                UnitPrice = reader.IsDBNull("UnitPrice") ? 0 : reader.GetInt32("UnitPrice"),
                Price = reader.IsDBNull("Price") ? 0 : reader.GetInt32("Price"),
                CrDateTime = reader.GetDateTime("CrDateTime")
            });
        }

        return results;
    }

    /// <summary>
    /// ItemInventories_wkの統計情報を取得する
    /// </summary>
    public async Task<WorkDataSummary> GetWorkDataSummaryAsync()
    {
        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        const string sql = @"
            SELECT
                COUNT(*) as TotalRecords,
                COUNT(DISTINCT StoreId) as StoreCount,
                COUNT(DISTINCT ItemId) as ItemCount,
                COUNT(DISTINCT SectionId) as SectionCount,
                MIN(InventoryDate) as MinInventoryDate,
                MAX(InventoryDate) as MaxInventoryDate,
                SUM(CAST(InventoryCount as bigint)) as TotalInventoryCount,
                SUM(CASE WHEN InventoryCount = 0 THEN 1 ELSE 0 END) as ZeroInventoryItemCount,
                MIN(InventoryCount) as MinInventoryCount,
                MAX(InventoryCount) as MaxInventoryCount,
                MIN(CrDateTime) as MinCreateDateTime,
                MAX(CrDateTime) as MaxCreateDateTime
            FROM dbo.ItemInventories_wk";

        using var cmd = new SqlCommand(sql, connection);
        using var reader = await cmd.ExecuteReaderAsync();

        if (await reader.ReadAsync())
        {
            return new WorkDataSummary
            {
                TotalRecords = reader.GetInt32("TotalRecords"),
                StoreCount = reader.GetInt32("StoreCount"),
                ItemCount = reader.GetInt32("ItemCount"),
                SectionCount = reader.GetInt32("SectionCount"),
                InventoryDate = reader.IsDBNull("MinInventoryDate") ? null : reader.GetDateTime("MinInventoryDate"),
                TotalInventoryCount = reader.IsDBNull("TotalInventoryCount") ? 0 : reader.GetInt64("TotalInventoryCount"),
                ZeroInventoryItemCount = reader.GetInt32("ZeroInventoryItemCount"),
                MinInventoryCount = reader.GetInt32("MinInventoryCount"),
                MaxInventoryCount = reader.GetInt32("MaxInventoryCount"),
                MinCreateDateTime = reader.IsDBNull("MinCreateDateTime") ? null : reader.GetDateTime("MinCreateDateTime"),
                MaxCreateDateTime = reader.IsDBNull("MaxCreateDateTime") ? null : reader.GetDateTime("MaxCreateDateTime")
            };
        }

        return new WorkDataSummary();
    }
    
    /// <summary>
    /// 本番登録処理（未実装）
    /// </summary>
    public async Task<ImportResult> RegisterToProductionAsync(IProgress<ImportProgress> progress)
    {
        // TODO: 本番登録処理は後で実装
        await Task.Delay(1000);
        return ImportResult.Success(0, 0);
    }
    
    /// <summary>
    /// 選択されたCSVファイルをInventoryAll_wkテーブルに投入する
    /// </summary>
    private async Task<int> ImportCsvFilesToInventoryAllWorkAsync(List<InventoryFileInfo> selectedFiles, IProgress<ImportProgress> progress)
    {
        var totalRecords = 0;
        var processedFiles = 0;

        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        foreach (var fileInfo in selectedFiles)
        {
            progress?.Report(new ImportProgress
            {
                Phase = $"CSVファイル読込中: {fileInfo.FileName}",
                ProgressPercentage = 10 + (processedFiles * 15 / selectedFiles.Count),
                CurrentFile = fileInfo.FileName,
                ProcessedRecords = totalRecords
            });

            try
            {
                var fileRecords = await ImportSingleCsvFileAsync(connection, fileInfo);
                totalRecords += fileRecords;
                processedFiles++;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"ファイル '{fileInfo.FileName}' の処理中にエラーが発生しました: {ex.Message}", ex);
            }
        }

        return totalRecords;
    }

    /// <summary>
    /// 単一のCSVファイルをInventoryAll_wkテーブルに投入する
    /// </summary>
    private async Task<int> ImportSingleCsvFileAsync(SqlConnection connection, InventoryFileInfo fileInfo)
    {
        var recordCount = 0;
        const int batchSize = 1000; // バッチサイズ
        var batch = new List<string[]>();

        try
        {
            // CSVファイルを読み込み
            if (!File.Exists(fileInfo.FilePath))
            {
                throw new FileNotFoundException($"CSVファイルが見つかりません: {fileInfo.FilePath}");
            }

            var lines = await File.ReadAllLinesAsync(fileInfo.FilePath);

            if (lines.Length <= 1)
            {
                throw new InvalidOperationException($"CSVファイルにデータがありません: {fileInfo.FileName}");
            }

            // ヘッダー行をスキップして処理
            for (int i = 1; i < lines.Length; i++)
            {
                var line = lines[i].Trim();
                if (string.IsNullOrEmpty(line)) continue;

                try
                {
                    // CSVの各列を解析（カンマ区切り、ダブルクォート対応）
                    var columns = ParseCsvLine(line);
                    if (columns.Length >= 4)
                    {
                        batch.Add(columns);

                        // バッチサイズに達したら一括挿入
                        if (batch.Count >= batchSize)
                        {
                            await InsertBatchToInventoryAllWork(connection, batch);
                            recordCount += batch.Count;
                            batch.Clear();
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw new InvalidOperationException($"CSV行の解析エラー (行{i + 1}): {ex.Message}", ex);
                }
            }

            // 残りのデータを挿入
            if (batch.Count > 0)
            {
                await InsertBatchToInventoryAllWork(connection, batch);
                recordCount += batch.Count;
            }

            return recordCount;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"CSVファイル処理中にエラーが発生しました ({fileInfo.FileName}): {ex.Message}", ex);
        }
    }

    /// <summary>
    /// CSV行を解析する（ダブルクォート対応）
    /// </summary>
    private string[] ParseCsvLine(string line)
    {
        var columns = new List<string>();
        var currentColumn = "";
        var inQuotes = false;

        for (int i = 0; i < line.Length; i++)
        {
            var c = line[i];

            if (c == '"')
            {
                inQuotes = !inQuotes;
            }
            else if (c == ',' && !inQuotes)
            {
                columns.Add(currentColumn.Trim('"'));
                currentColumn = "";
            }
            else
            {
                currentColumn += c;
            }
        }

        // 最後の列を追加
        columns.Add(currentColumn.Trim('"'));

        return columns.ToArray();
    }

    /// <summary>
    /// バッチデータをInventoryAll_wkテーブルに一括挿入する
    /// </summary>
    private async Task InsertBatchToInventoryAllWork(SqlConnection connection, List<string[]> batch)
    {
        const string sql = @"
            INSERT INTO dbo.InventoryAll_wk ([列 0], [列 1], [列 2], [列 3])
            VALUES (@Col0, @Col1, @Col2, @Col3)";

        using var transaction = connection.BeginTransaction();
        try
        {
            foreach (var columns in batch)
            {
                using var cmd = new SqlCommand(sql, connection, transaction);
                cmd.Parameters.AddWithValue("@Col0", columns[0]);
                cmd.Parameters.AddWithValue("@Col1", columns[1]);
                cmd.Parameters.AddWithValue("@Col2", columns[2]);
                cmd.Parameters.AddWithValue("@Col3", columns[3]);

                await cmd.ExecuteNonQueryAsync();
            }

            await transaction.CommitAsync();
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }
    
    /// <summary>
    /// InventoryAll_wkからItemInventories_wkへの基本的なマスタ結合処理
    /// パフォーマンス向上のため、複雑なビューを使わずシンプルなJOINで実装
    /// </summary>
    private async Task ProcessBasicMasterJoinAsync(DateTime registrationDate, IProgress<ImportProgress> progress)
    {
        try
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            // まず、データの存在確認
            var inventoryCount = await GetInventoryAllWorkCountAsync(connection);
            if (inventoryCount == 0)
            {
                throw new InvalidOperationException("InventoryAll_wkテーブルにデータがありません");
            }

            progress?.Report(new ImportProgress
            {
                Phase = $"マスタ結合処理中... ({inventoryCount:N0}件のCSVデータを処理)",
                ProgressPercentage = 32,
                ProcessedRecords = inventoryCount
            });

            // 基本的なマスタ結合のみ実行（SectionIdは後で別途更新）
            const string sql = @"
                INSERT INTO dbo.ItemInventories_wk
                    (StoreId, ItemId, InventoryCount, SectionId, LastUpdateUserId, DelFlg, InventoryDate, CrDateTime, UpDateTime)
                SELECT
                    s.Id as StoreId,
                    i.Id as ItemId,
                    CAST(w.[列 3] as int) as InventoryCount,
                    i.SectionId as SectionId,
                    12 as LastUpdateUserId,
                    0 as DelFlg,
                    CAST(w.[列 1] as date) as InventoryDate,
                    @RegistrationDate as CrDateTime,
                    @RegistrationDate as UpDateTime
                FROM dbo.InventoryAll_wk w
                INNER JOIN dbo.Stores s ON w.[列 0] = s.StoreCode
                INNER JOIN dbo.Items i ON w.[列 2] = i.ItemCode
                WHERE w.[列 0] IS NOT NULL
                  AND w.[列 1] IS NOT NULL
                  AND w.[列 2] IS NOT NULL
                  AND w.[列 3] IS NOT NULL
                  AND i.DelFlg = 0
                  AND ISNUMERIC(w.[列 3]) = 1";

        using var transaction = connection.BeginTransaction();
        try
        {
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@RegistrationDate", registrationDate);
            cmd.CommandTimeout = 300; // 5分のタイムアウト

            var processedRecords = await cmd.ExecuteNonQueryAsync();

            await transaction.CommitAsync();

            progress?.Report(new ImportProgress
            {
                Phase = "マスタ結合処理完了",
                ProgressPercentage = 40,
                ProcessedRecords = processedRecords,
                DetailMessage = $"{processedRecords:N0}件のレコードを処理しました"
            });
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            throw new InvalidOperationException($"マスタ結合処理中にエラーが発生しました: {ex.Message}", ex);
        }
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"マスタ結合処理の初期化中にエラーが発生しました: {ex.Message}", ex);
        }
    }
    
    /// <summary>
    /// SectionId情報を更新する
    /// 元のV_InvUpdate_Workビューの3層Sections結合を簡素化
    /// </summary>
    private async Task UpdateSectionInfoAsync(IProgress<ImportProgress> progress)
    {
        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        // 基本的なマスタ結合で既にSectionIdは設定済みなので、
        // 必要に応じて階層的なSectionId（親部門）への更新を行う
        // 現在は元のItemsテーブルのSectionIdをそのまま使用

        // 将来的に階層的なSection処理が必要な場合は以下のようなSQLを実行
        /*
        const string sql = @"
            UPDATE wk
            SET SectionId = s2.Id
            FROM dbo.ItemInventories_wk wk
            INNER JOIN dbo.Items i ON wk.ItemId = i.Id
            INNER JOIN dbo.Sections s ON i.SectionId = s.Id
            INNER JOIN dbo.Sections s1 ON s.ParentCodeId = s1.Id
            INNER JOIN dbo.Sections s2 ON s1.ParentCodeId = s2.Id
            WHERE s2.Id IS NOT NULL";
        */

        // 現在は何もしない（基本マスタ結合で十分）
        await Task.Delay(50);

        progress?.Report(new ImportProgress
        {
            Phase = "部門情報設定完了",
            ProgressPercentage = 55,
            DetailMessage = "部門情報の設定が完了しました"
        });
    }
    
    /// <summary>
    /// 在庫0商品補完処理
    /// ItemInventoriesQuantテーブルを参照して、棚卸データにない商品を在庫0として追加
    /// 元のV_InvZeroUpdate_Workビューの処理を簡素化
    /// </summary>
    private async Task<int> ProcessZeroInventoryItemsAsync(DateTime registrationDate, IProgress<ImportProgress> progress)
    {
        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        // 棚卸日を取得（ItemInventories_wkから）
        var inventoryDate = await GetInventoryDateFromWorkTableAsync(connection);
        if (!inventoryDate.HasValue)
        {
            progress?.Report(new ImportProgress
            {
                Phase = "在庫0商品補完スキップ",
                ProgressPercentage = 70,
                DetailMessage = "棚卸日が取得できないため、在庫0商品補完をスキップしました"
            });
            return 0;
        }

        // 在庫0商品を追加するSQL（簡素化版）
        const string sql = @"
            INSERT INTO dbo.ItemInventories_wk
                (StoreId, ItemId, SectionId, LastUpdateUserId, DelFlg, CrDateTime, UpDateTime, InventoryDate, InventoryCount)
            SELECT DISTINCT
                q.StoreId,
                q.ItemId,
                i.SectionId,
                12 as LastUpdateUserId,
                0 as DelFlg,
                @RegistrationDate as CrDateTime,
                @RegistrationDate as UpDateTime,
                @InventoryDate as InventoryDate,
                0 as InventoryCount
            FROM dbo.ItemInventoriesQuant q
            INNER JOIN dbo.Items i ON q.ItemId = i.Id
            INNER JOIN dbo.Sections s ON i.SectionId = s.Id
            LEFT JOIN dbo.ItemInventories_wk wk ON q.StoreId = wk.StoreId AND q.ItemId = wk.ItemId
            WHERE q.Quantity <> 0
              AND wk.StoreId IS NULL
              AND i.DelFlg = 0
              AND q.StockDateFrom <= @InventoryDate
              AND q.StockDateTo >= @InventoryDate
              AND EXISTS (SELECT 1 FROM dbo.ItemInventories_wk wk2 WHERE wk2.StoreId = q.StoreId)";

        using var transaction = connection.BeginTransaction();
        try
        {
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.Parameters.AddWithValue("@RegistrationDate", registrationDate);
            cmd.Parameters.AddWithValue("@InventoryDate", inventoryDate.Value);
            cmd.CommandTimeout = 300; // 5分のタイムアウト

            var addedRecords = await cmd.ExecuteNonQueryAsync();

            await transaction.CommitAsync();

            progress?.Report(new ImportProgress
            {
                Phase = "在庫0商品補完完了",
                ProgressPercentage = 75,
                ProcessedRecords = addedRecords,
                DetailMessage = $"{addedRecords:N0}件の在庫0商品を追加しました"
            });

            return addedRecords;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    /// <summary>
    /// ItemInventories_wkテーブルから棚卸日を取得する
    /// </summary>
    private async Task<DateTime?> GetInventoryDateFromWorkTableAsync(SqlConnection connection)
    {
        const string sql = "SELECT TOP 1 InventoryDate FROM dbo.ItemInventories_wk";

        using var cmd = new SqlCommand(sql, connection);
        var result = await cmd.ExecuteScalarAsync();

        return result as DateTime?;
    }
    
    /// <summary>
    /// 売価情報更新処理
    /// Itemsテーブルから売価情報を取得してItemInventories_wkを更新
    /// </summary>
    private async Task UpdatePriceInfoAsync(IProgress<ImportProgress> progress)
    {
        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        const string sql = @"
            UPDATE wk
            SET
                UnitPrice = i.RegularPrice,
                Price = i.RegularPrice * wk.InventoryCount,
                UnitPriceTaxIn = i.RegularPriceTaxIn,
                PriceTaxIn = i.RegularPriceTaxIn * wk.InventoryCount
            FROM dbo.ItemInventories_wk wk
            INNER JOIN dbo.Items i ON wk.ItemId = i.Id
            WHERE i.RegularPrice IS NOT NULL";

        using var transaction = connection.BeginTransaction();
        try
        {
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.CommandTimeout = 300; // 5分のタイムアウト

            var updatedRecords = await cmd.ExecuteNonQueryAsync();

            await transaction.CommitAsync();

            progress?.Report(new ImportProgress
            {
                Phase = "売価情報更新完了",
                ProgressPercentage = 85,
                ProcessedRecords = updatedRecords,
                DetailMessage = $"{updatedRecords:N0}件のレコードの売価情報を更新しました"
            });
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }
    
    /// <summary>
    /// 重複データ削除処理
    /// 定期棚卸日以降に店舗が棚卸したデータがあれば、ItemInventories_wkから削除
    /// memo.txtの⑧番の処理に相当
    /// </summary>
    private async Task<int> RemoveDuplicateDataAsync(IProgress<ImportProgress> progress)
    {
        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        const string sql = @"
            DELETE wk
            FROM dbo.ItemInventories_wk wk
            INNER JOIN dbo.ItemInventories ii ON
                wk.ItemId = ii.ItemId
                AND wk.StoreId = ii.StoreId
                AND wk.InventoryDate <= ii.InventoryDate";

        using var transaction = connection.BeginTransaction();
        try
        {
            using var cmd = new SqlCommand(sql, connection, transaction);
            cmd.CommandTimeout = 300; // 5分のタイムアウト

            var deletedRecords = await cmd.ExecuteNonQueryAsync();

            await transaction.CommitAsync();

            progress?.Report(new ImportProgress
            {
                Phase = "重複データ削除完了",
                ProgressPercentage = 95,
                ProcessedRecords = deletedRecords,
                DetailMessage = $"{deletedRecords:N0}件の重複レコードを削除しました"
            });

            return deletedRecords;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }
    
    private async Task<int> GetProcessedRecordCountAsync()
    {
        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();

        using var cmd = new SqlCommand("SELECT COUNT(*) FROM dbo.ItemInventories_wk", connection);
        var result = await cmd.ExecuteScalarAsync();
        return Convert.ToInt32(result);
    }

    /// <summary>
    /// InventoryAll_wkテーブルのレコード数を取得
    /// </summary>
    private async Task<int> GetInventoryAllWorkCountAsync(SqlConnection connection)
    {
        using var cmd = new SqlCommand("SELECT COUNT(*) FROM dbo.InventoryAll_wk", connection);
        var result = await cmd.ExecuteScalarAsync();
        return Convert.ToInt32(result);
    }
}
