# 棚卸結果CSV自動取込システム開発計画書

## 1. プロジェクト概要

### 目的
年2回実施される店舗別棚卸結果CSVファイルの取込処理をUIアプリケーションで効率化し、手動作業の省力化と処理の標準化を図る。

### 対象範囲
- 棚卸結果CSVファイルの選択・表示機能
- 複数店舗データの統合処理
- 統合ワークテーブルでのデータ管理
- データ確認・閲覧機能
- 基幹システムへの登録機能

## 2. 現状分析

### 現在の手動プロセス
1. InventoryALL_XXX.csvファイルをSSMSで手動インポート
　　”XXX”は、3桁の店舗コード。
　　親フォルダに”XXX”（店舗コード）ディレクトリがあり、その中に　InventoryALL_XXX.csvファイルが存在する。
2. ワークテーブルのクリア処理
　　取込前に前回処理済みのワークテーブルをクリアする。
3. ビューの作成日時・更新日時の手動設定変更
4. 複数店舗データのInventoryALL_wkへの統合
5. V_InvUpdate_Workビューからの変換処理
6. 在庫ゼロ商品の補完処理
7. 売価情報の更新
8. 重複データの削除
9. 本番テーブルへの投入
10. テーブル最適化
11. シノプス連携ファイル出力

### 課題
- 手動作業による人的ミスのリスク
- ファイル選択・確認作業の煩雑さ
- データ投入前の確認機能不足
- 処理状況の可視化不足
- SSMSでの直接操作によるリスク

## 3. システム設計

### 3.1 アーキテクチャ

```
[UIアプリケーション] → [ファイル選択] → [データ統合] → [確認画面] → [本番登録]
        ↓                 ↓             ↓           ↓          ↓
[設定読込・表示]    [CSVインポート]  [ワークテーブル]  [グリッド表示]  [ItemInventory]
```

### 3.2 技術構成
- **プラットフォーム**: .NET 6.0 / C#
- **UI フレームワーク**: Windows Forms または WPF
- **データベース**: SQL Server（既存のYsskKikanデータベース）
- **ログ管理**: NLog
- **設定管理**: appsettings.json

### 3.3 主要コンポーネント

#### 3.3.1 メインフォーム (MainForm)
- アプリケーションの起動と設定読込
- 親ディレクトリの参照・選択機能
- 処理状況の表示とユーザー操作制御

#### 3.3.2 ファイル選択コントロール (FileSelectionControl)
- 店舗コードディレクトリの検索・表示
- CSVファイルの一覧表示とチェック選択
- ファイル情報の表示（ファイル名、更新日時、サイズ等）

#### 3.3.3 データインポートサービス (DataImportService)
- 選択されたCSVファイルの読込・検証
- 統合ワークテーブルへの一括インポート
- マスタデータとの紐づけ（StoreId、ItemId、SectionId変換）

#### 3.3.4 データ統合・補完サービス (DataIntegrationService)
- InventoryQuantテーブルとの結合処理
- 在庫0以外商品の0レコード追加
- Items標準売価の設定
- 未来日付の棚卸訂正レコード削除

#### 3.3.5 データ確認コントロール (DataReviewControl)
- 統合ワークテーブルデータのグリッド表示
- データ内容の確認・閲覧機能
- 処理対象レコード数の表示

#### 3.3.6 データ登録サービス (DataRegistrationService)
- "本番登録"ボタンによるItemInventoryテーブルへのInsert
- トランザクション制御
- 登録完了通知

## 4. データベース設計

### 4.1 新規テーブル

#### InventoryImportLogs（インポートログテーブル）
```sql
CREATE TABLE InventoryImportLogs (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    ImportDate DATE NOT NULL,
    ImportedFiles NVARCHAR(MAX) NOT NULL, -- JSON形式でファイル一覧を保存
    TotalRecordCount INT NOT NULL,
    ProcessStatus NVARCHAR(50) NOT NULL, -- 'InProgress', 'Completed', 'Error'
    ErrorMessage NVARCHAR(MAX),
    StartDateTime DATETIME2 NOT NULL,
    EndDateTime DATETIME2,
    CreatedBy NVARCHAR(100) DEFAULT 'InventoryApp'
);
```

#### InventoryWorkIntegrated（統合ワークテーブル）
```sql
CREATE TABLE InventoryWorkIntegrated (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    InventoryDate DATE NOT NULL,
    StoreId BIGINT NOT NULL,
    ItemId BIGINT NOT NULL,
    SectionId BIGINT NOT NULL,
    InventoryCount INT NOT NULL,
    UnitPrice INT NULL,
    Price INT NULL,
    UnitPriceTaxIn INT NULL,
    PriceTaxIn INT NULL,
    SourceType NVARCHAR(20) NOT NULL, -- 'CSV', 'ZERO_FILL'
    SourceFileName NVARCHAR(255),
    CreatedDateTime DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (StoreId) REFERENCES Stores(Id),
    FOREIGN KEY (ItemId) REFERENCES Items(Id),
    FOREIGN KEY (SectionId) REFERENCES Sections(Id)
);
```

### 4.2 既存テーブルの利用
- `ItemInventories`（最終投入先）
- `ItemInventoriesQuant`（在庫数量テーブル）
- `Items`（商品マスタ）
- `Stores`（店舗マスタ）
- `Sections`（部門マスタ）

## 5. 詳細仕様

### 5.1 ファイル仕様
- **ファイル構造**: 
  - 親ディレクトリ下に店舗コード（3桁）のサブディレクトリ
  - 各店舗ディレクトリ内に`InventoryALL_{店舗コード}.csv`ファイル
- **文字コード**: UTF-8 / Shift-JIS 自動判定
- **列構成**: 店舗コード, 棚卸日, 商品コード, 在庫数
- **区切り文字**: カンマ（`,`）
- **囲み文字**: ダブルクォート（`"`）

### 5.2 操作フロー

```mermaid
graph TD
    A[アプリ起動] --> B[設定読込]
    B --> C[親ディレクトリ選択]
    C --> D[店舗ディレクトリ検索]
    D --> E[CSVファイル一覧表示]
    E --> F[ファイル選択]
    F --> G[統合ワークテーブルクリア]
    G --> H[選択ファイルインポート]
    H --> I[マスタ紐づけ]
    I --> J[在庫0商品補完]
    J --> K[売価情報設定]
    K --> L[重複削除]
    L --> M[データ確認画面表示]
    M --> N{データ確認OK?}
    N -->|No| M
    N -->|Yes| O[本番登録実行]
    O --> P[完了通知]
```

### 5.3 詳細処理仕様

#### 5.3.1 ファイル選択・インポート処理
1. **親ディレクトリ選択**: ユーザーが親ディレクトリを選択
2. **店舗ディレクトリ検索**: 3桁数字のサブディレクトリを自動検索
3. **CSVファイル検出**: 各店舗ディレクトリ内の`InventoryALL_XXX.csv`を検索
4. **ファイル情報表示**: ファイル名、更新日時、サイズ、レコード数を表示
5. **ファイル選択**: チェックボックスで複数ファイルを選択可能

#### 5.3.2 データ統合処理
1. **統合ワークテーブルクリア**: `InventoryWorkIntegrated`テーブルを初期化
2. **CSVデータ読込**: 選択されたファイルを順次読込
3. **マスタ紐づけ**: 
   - 店舗コード → `Stores.Id` (StoreId)
   - 商品コード → `Items.Id` (ItemId)  
   - 商品の部門 → `Sections.Id` (SectionId)
4. **統合テーブル投入**: 変換されたデータを`InventoryWorkIntegrated`に挿入

#### 5.3.3 在庫補完処理
1. **InventoryQuantテーブル結合**: 該当日の在庫数量データを取得
2. **在庫0以外商品抽出**: `InventoryQuant`で在庫数量が0以外の商品を特定
3. **0レコード追加**: 棚卸データにない商品について在庫数0のレコードを追加
4. **売価情報設定**: `Items.RegularPrice`から標準売価を設定
5. **重複削除**: 店舗・商品・棚卸日の組み合わせで重複レコードを削除
6. **未来日付削除**: 棚卸日より未来の`ItemInventory`レコードがある商品をワークから除外

#### 5.3.4 確認・登録処理
1. **データ確認表示**: 統合ワークテーブルの内容をグリッドで表示
2. **データ検索・フィルタ**: 店舗・商品・部門での絞り込み機能
3. **本番登録実行**: "本番登録"ボタンクリックで`ItemInventories`テーブルにInsert
4. **トランザクション制御**: 全件成功または全件ロールバック

### 5.4 エラーハンドリング
- CSVファイル形式エラー時の詳細メッセージ表示
- マスタ紐づけ失敗時の該当レコード表示
- データベース接続エラー時の再試行機能
- 処理中断時の安全な状態復旧

### 5.5 設定項目
```json
{
  "InventoryImportApp": {
    "DefaultParentFolder": "D:\\Inventory\\Input",
    "TargetStores": ["347", "351", "353", "355", "360", "362", "363", "364", "365", "366", "367", "443", "444"],
    "DatabaseConnectionString": "Data Source=...;Initial Catalog=YsskKikan;...",
    "UI": {
      "WindowTitle": "棚卸結果取込システム",
      "GridPageSize": 1000,
      "ConfirmationRequired": true
    },
    "Import": {
      "MaxFileSize": 52428800,
      "SupportedEncodings": ["UTF-8", "Shift_JIS"],
      "RequiredColumns": ["店舗コード", "棚卸日", "商品コード", "在庫数"]
    }
  }
}
```

## 6. 開発工程

### Phase 1: 基盤開発（3週間）
- **Week 1**: プロジェクト基盤構築
  - Visual Studio プロジェクト作成（Windows Forms/.NET 6.0）
  - NuGetパッケージ導入（Entity Framework, NLog, etc.）
  - データベーステーブル作成（InventoryWorkIntegrated等）

- **Week 2-3**: 基本UI開発
  - MainForm基本レイアウト作成
  - 設定読込機能実装
  - ファイル・フォルダ選択機能実装

### Phase 2: コア機能開発（4週間）
- **Week 4**: ファイル選択機能
  - FileSelectionControl実装
  - 店舗ディレクトリ検索・表示機能
  - CSVファイル一覧表示・選択機能

- **Week 5**: データインポート機能
  - DataImportService実装
  - CSVファイル読込・検証機能
  - マスタデータ紐づけ機能

- **Week 6-7**: データ統合・補完機能
  - DataIntegrationService実装
  - InventoryQuantテーブル連携
  - 在庫0商品補完・売価設定機能

### Phase 3: 確認・登録機能開発（3週間）
- **Week 8-9**: データ確認機能
  - DataReviewControl実装
  - グリッドによるデータ表示機能
  - データ内容確認・検索機能

- **Week 10**: 本番登録機能
  - DataRegistrationService実装
  - ItemInventoryテーブルへの登録機能
  - トランザクション制御・エラーハンドリング

### Phase 4: テスト・運用準備（2週間）
- **Week 11**: 統合テスト
  - 全機能統合テスト
  - ユーザビリティテスト
  - 性能・例外処理テスト

- **Week 12**: 運用準備・リリース
  - ユーザーマニュアル作成
  - 本番環境セットアップ
  - ユーザー操作研修・本番稼働

## 7. リスク管理

### 7.1 技術的リスク
- **データ不整合**: 詳細なデータ検証とトランザクション制御で対応
- **性能問題**: 大量データ処理時のプログレスバー表示とメモリ最適化
- **UI応答性**: 非同期処理による画面フリーズ防止

### 7.2 運用リスク
- **ファイル選択ミス**: ファイル情報表示と確認ダイアログで対応
- **データ投入前確認不足**: グリッド表示による内容確認を必須化
- **操作ミス**: 重要操作前の確認ダイアログと取り消し機能

## 8. 成果物

### 8.1 ソフトウェア
- InventoryImportApp.exe（メインアプリケーション）
- 設定ファイル（appsettings.json）
- データベーススクリプト（テーブル作成・初期データ）
- インストーラー（ClickOnce配置）

### 8.2 ドキュメント
- システム仕様書
- ユーザー操作マニュアル
- 管理者向け運用ガイド
- トラブルシューティングガイド

### 8.3 テスト資料
- テスト仕様書
- テストデータセット
- 操作手順テスト結果

## 9. 保守運用

### 9.1 監視項目
- アプリケーション動作確認
- データベース接続状況
- ログファイルサイズ
- インポート処理履歴

### 9.2 定期メンテナンス
- ログファイルの定期削除
- InventoryWorkIntegratedテーブルの古いデータ削除
- データベース統計情報更新
- 設定ファイルのバックアップ

### 9.3 システム更新
- 店舗マスタ追加・変更対応
- CSVファイル形式変更対応
- UI改善・機能追加対応
- .NET Framework更新対応

## 10. 費用対効果

### 10.1 開発コスト
- 開発期間: 12週間（約3ヶ月）
- 開発リソース: SE 1名 + プログラマー 1名
- インフラコスト: クライアントPC上で動作（追加コストなし）

### 10.2 運用メリット
- 手動作業時間削減: 年間約20時間 → 6時間（70%削減）
- データ確認機能追加: 投入前の品質向上
- 操作ミス削減: UI化による直感的操作
- SSMSスキル不要: 一般ユーザーでも操作可能

### 10.3 ROI
- 年間削減時間: 14時間
- 時間単価: 3,000円/時間
- 年間削減効果: 42,000円
- 作業品質向上による間接効果: 年間約50,000円
- 総合年間効果: 約92,000円
- 3年間でのROI: 約276,000円

## 11. まとめ

本システムの導入により、棚卸結果の取込処理がUIアプリケーション化され、SSMS操作に依存せずに安全かつ効率的な処理が可能となります。特にデータ確認機能により投入前の品質確保が実現できます。

開発期間は約3ヶ月を想定しており、既存の手動プロセスを段階的に置き換えることで安全な導入が可能です。UIベースのため直感的な操作が可能で、ユーザーの習熟コストも最小限に抑えられます。 