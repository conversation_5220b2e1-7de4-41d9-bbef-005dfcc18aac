USE [Yssk<PERSON>ikan]
GO

/****** Object:  Table [dbo].[Items]    Script Date: 2025/07/30 17:41:45 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Items](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[ItemDivisionId] [bigint] NOT NULL,
	[ItemCode] [varchar](7) NOT NULL,
	[SectionId] [bigint] NOT NULL,
	[ManufactureName] [nvarchar](10) NOT NULL,
	[ItemName] [nvarchar](20) NOT NULL,
	[ItemSpec] [nvarchar](10) NOT NULL,
	[ItemNameKana] [nvarchar](25) NOT NULL,
	[NameOnVoucher] [nvarchar](14) NOT NULL,
	[AlcContent] [int] NOT NULL,
	[ParentItemId] [bigint] NULL,
	[TagCode] [varchar](13) NOT NULL,
	[RegularCost] [decimal](18, 2) NOT NULL,
	[RegularPrice] [int] NOT NULL,
	[RegularPriceTaxIn] [int] NOT NULL,
	[RcvUnit] [int] NOT NULL,
	[LeadTime] [int] NULL,
	[OrderTimeId] [bigint] NULL,
	[TaxDivisionCode] [varchar](50) NOT NULL,
	[StoreGroupId] [bigint] NOT NULL,
	[SalesSt] [date] NOT NULL,
	[SalesEd] [date] NOT NULL,
	[OrderEd] [date] NOT NULL,
	[ItemSizeW] [int] NOT NULL,
	[ItemSizeH] [int] NOT NULL,
	[ItemSizeD] [int] NOT NULL,
	[SupplierId] [bigint] NOT NULL,
	[GrossDeliveryId] [bigint] NULL,
	[Remarks] [nvarchar](max) NOT NULL,
	[LastRcvDate] [date] NULL,
	[LastSalesDate] [date] NULL,
	[DelFlg] [bigint] NOT NULL,
	[CrDateTime] [datetime] NOT NULL,
	[UpDateTime] [datetime] NOT NULL,
	[LastUpdateUserId] [bigint] NOT NULL,
	[OrderSuspendFlg] [bit] NOT NULL,
	[OrderListDisplay] [bit] NOT NULL,
	[SupplierItemCode] [varchar](13) NULL,
	[ColorName] [nvarchar](10) NULL,
	[SizeCodeId] [bigint] NULL,
	[SpecCodeId] [bigint] NULL,
	[MMCodeId] [bigint] NULL,
	[RetailSuggestedPrice] [int] NOT NULL,
	[SellbyDate] [int] NOT NULL,
	[ExpirationDate] [int] NOT NULL,
	[VoidDiscountFlg] [bit] NOT NULL,
	[UnitPriceChangeFlg] [bit] NOT NULL,
	[ReviewValid] [varchar](3) NULL,
	[LimitValid] [varchar](3) NULL,
	[Capacity] [int] NULL,
	[UnitId] [bigint] NULL,
	[Unit] [int] NULL,
	[Comment] [nvarchar](36) NULL,
 CONSTRAINT [PK_Items] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_ItemDivision]  DEFAULT ((0)) FOR [ItemDivisionId]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_ItemCode]  DEFAULT ('') FOR [ItemCode]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_SectionId]  DEFAULT ((0)) FOR [SectionId]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_ManufactureName]  DEFAULT ('') FOR [ManufactureName]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_ItemName]  DEFAULT ('') FOR [ItemName]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_ItemSpec]  DEFAULT ('') FOR [ItemSpec]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_ItemNameKana]  DEFAULT ('') FOR [ItemNameKana]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_NameOnVoucher]  DEFAULT ('') FOR [NameOnVoucher]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_AlcContent]  DEFAULT ((0)) FOR [AlcContent]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_TagCode]  DEFAULT ('') FOR [TagCode]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_RegularCost]  DEFAULT ((0)) FOR [RegularCost]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_RegularPrice]  DEFAULT ((0)) FOR [RegularPrice]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_RegularPrice1]  DEFAULT ((0)) FOR [RegularPriceTaxIn]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_RcvUnit]  DEFAULT ((0)) FOR [RcvUnit]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_TaxDivisionCode]  DEFAULT ('') FOR [TaxDivisionCode]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_StoreGroupId]  DEFAULT ((0)) FOR [StoreGroupId]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_SalesSt]  DEFAULT (getdate()) FOR [SalesSt]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_SalesEd]  DEFAULT ('9999/12/31') FOR [SalesEd]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_OrderEd]  DEFAULT ('9999/12/31') FOR [OrderEd]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_ItemSizeW]  DEFAULT ((0)) FOR [ItemSizeW]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_ItemSizeW1]  DEFAULT ((0)) FOR [ItemSizeH]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_ItemSizeW2]  DEFAULT ((0)) FOR [ItemSizeD]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_SupplierId]  DEFAULT ((0)) FOR [SupplierId]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_Note]  DEFAULT ('') FOR [Remarks]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_DelFlg]  DEFAULT ((0)) FOR [DelFlg]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_CrDate]  DEFAULT (getdate()) FOR [CrDateTime]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_Update]  DEFAULT (getdate()) FOR [UpDateTime]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_LastUpdateUser]  DEFAULT ((0)) FOR [LastUpdateUserId]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_OrderSuspendFlg]  DEFAULT ((0)) FOR [OrderSuspendFlg]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_OrderListDisplay]  DEFAULT ((1)) FOR [OrderListDisplay]
GO

ALTER TABLE [dbo].[Items] ADD  DEFAULT ((0)) FOR [RetailSuggestedPrice]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_SellbyDate]  DEFAULT ((0)) FOR [SellbyDate]
GO

ALTER TABLE [dbo].[Items] ADD  CONSTRAINT [DF_Items_ExpirationDate]  DEFAULT ((0)) FOR [ExpirationDate]
GO

ALTER TABLE [dbo].[Items] ADD  DEFAULT ((0)) FOR [VoidDiscountFlg]
GO

ALTER TABLE [dbo].[Items] ADD  DEFAULT ((0)) FOR [UnitPriceChangeFlg]
GO

ALTER TABLE [dbo].[Items]  WITH CHECK ADD  CONSTRAINT [FK_Items_GaneralMasterValuesItemDivisionId] FOREIGN KEY([ItemDivisionId])
REFERENCES [dbo].[GeneralMasterValues] ([Id])
GO

ALTER TABLE [dbo].[Items] CHECK CONSTRAINT [FK_Items_GaneralMasterValuesItemDivisionId]
GO

ALTER TABLE [dbo].[Items]  WITH CHECK ADD  CONSTRAINT [FK_Items_GaneralMasterValuesOrderTimeId] FOREIGN KEY([OrderTimeId])
REFERENCES [dbo].[GeneralMasterValues] ([Id])
GO

ALTER TABLE [dbo].[Items] CHECK CONSTRAINT [FK_Items_GaneralMasterValuesOrderTimeId]
GO

ALTER TABLE [dbo].[Items]  WITH CHECK ADD  CONSTRAINT [FK_Items_GeneralMasterValuesGrossDeliveryId] FOREIGN KEY([GrossDeliveryId])
REFERENCES [dbo].[GeneralMasterValues] ([Id])
GO

ALTER TABLE [dbo].[Items] CHECK CONSTRAINT [FK_Items_GeneralMasterValuesGrossDeliveryId]
GO

ALTER TABLE [dbo].[Items]  WITH CHECK ADD  CONSTRAINT [FK_Items_Sections] FOREIGN KEY([SectionId])
REFERENCES [dbo].[Sections] ([Id])
GO

ALTER TABLE [dbo].[Items] CHECK CONSTRAINT [FK_Items_Sections]
GO

ALTER TABLE [dbo].[Items]  WITH CHECK ADD  CONSTRAINT [FK_Items_StoreGroups] FOREIGN KEY([StoreGroupId])
REFERENCES [dbo].[StoreGroups] ([Id])
GO

ALTER TABLE [dbo].[Items] CHECK CONSTRAINT [FK_Items_StoreGroups]
GO

ALTER TABLE [dbo].[Items]  WITH CHECK ADD  CONSTRAINT [FK_Items_Suppliers] FOREIGN KEY([SupplierId])
REFERENCES [dbo].[Suppliers] ([Id])
GO

ALTER TABLE [dbo].[Items] CHECK CONSTRAINT [FK_Items_Suppliers]
GO

ALTER TABLE [dbo].[Items]  WITH CHECK ADD  CONSTRAINT [FK_Items_Users] FOREIGN KEY([LastUpdateUserId])
REFERENCES [dbo].[Users] ([Id])
GO

ALTER TABLE [dbo].[Items] CHECK CONSTRAINT [FK_Items_Users]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'GeneralMasters.Code = 1,商品マスタ区分' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'ItemDivisionId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'商品コード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'ItemCode'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部門Id(Class,CodeLevel=2)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'SectionId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'メーカ名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'ManufactureName'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'商品名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'ItemName'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'商品規格' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'ItemSpec'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'商品名カナ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'ItemNameKana'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'レシート名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'NameOnVoucher'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'アルコール量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'AlcContent'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'親商品Id' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'ParentItemId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'2段タグコード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'TagCode'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'原単価' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'RegularCost'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'税抜売単価' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'RegularPrice'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'税込売単価' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'RegularPriceTaxIn'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'入り数' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'RcvUnit'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'リードタイム' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'LeadTime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'GeneralMasters.Code = 2,発注締区分' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'OrderTimeId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'税区分コード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'TaxDivisionCode'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'店舗グループId' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'StoreGroupId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'販売開始日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'SalesSt'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'販売終了日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'SalesEd'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'発注終了日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'OrderEd'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'商品サイズ幅' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'ItemSizeW'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'商品サイズ高さ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'ItemSizeH'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'商品サイズ奥行き' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'ItemSizeD'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'仕入先Id' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'SupplierId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'GeneralMasters.Code = 12,総量納品区分' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'GrossDeliveryId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'備考' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'Remarks'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最終仕入日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'LastRcvDate'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最終販売日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'LastSalesDate'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'削除フラグ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'DelFlg'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'作成日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'CrDateTime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'UpDateTime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最終更新ユーザID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'LastUpdateUserId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'検討' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'ReviewValid'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'限度' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'LimitValid'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'容量' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'Capacity'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'単位ID(GeneralMastervalues.Id)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'UnitId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'単位' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'Unit'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'コメント' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items', @level2type=N'COLUMN',@level2name=N'Comment'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'商品' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Items'
GO


