USE [YsskKikan]
GO

/****** Object:  Table [dbo].[ItemInventoriesQuant]    Script Date: 2025/07/30 17:50:00 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ItemInventoriesQuant](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[StoreId] [bigint] NOT NULL,
	[ItemId] [bigint] NOT NULL,
	[StockDateFrom] [date] NOT NULL,
	[StockDateTo] [date] NOT NULL,
	[Quantity] [int] NOT NULL,
	[DelFlg] [bigint] NOT NULL,
	[CrDateTime] [datetime] NOT NULL,
	[UpDateTime] [datetime] NOT NULL,
	[LastUpdateUserId] [bigint] NOT NULL,
 CONSTRAINT [PK_ItemInventoriesQuant] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ItemInventoriesQuant] ADD  CONSTRAINT [DF_ItemInventoriesQuant_StoreId]  DEFAULT ((0)) FOR [StoreId]
GO

ALTER TABLE [dbo].[ItemInventoriesQuant] ADD  CONSTRAINT [DF_ItemInventoriesQuant_Quantity]  DEFAULT ((0)) FOR [Quantity]
GO

ALTER TABLE [dbo].[ItemInventoriesQuant] ADD  CONSTRAINT [DF_ItemInventoriesQuant_DelFlg]  DEFAULT ((0)) FOR [DelFlg]
GO

ALTER TABLE [dbo].[ItemInventoriesQuant] ADD  CONSTRAINT [DF_ItemInventoriesQuant_CrDateTime]  DEFAULT (getdate()) FOR [CrDateTime]
GO

ALTER TABLE [dbo].[ItemInventoriesQuant] ADD  CONSTRAINT [DF_ItemInventoriesQuant_UpDateTime]  DEFAULT (getdate()) FOR [UpDateTime]
GO

ALTER TABLE [dbo].[ItemInventoriesQuant] ADD  CONSTRAINT [DF_ItemInventoriesQuant_LastUpdateUserId]  DEFAULT ((0)) FOR [LastUpdateUserId]
GO

ALTER TABLE [dbo].[ItemInventoriesQuant]  WITH CHECK ADD  CONSTRAINT [FK_ItemInventoriesQuant_Items] FOREIGN KEY([ItemId])
REFERENCES [dbo].[Items] ([Id])
GO

ALTER TABLE [dbo].[ItemInventoriesQuant] CHECK CONSTRAINT [FK_ItemInventoriesQuant_Items]
GO

ALTER TABLE [dbo].[ItemInventoriesQuant]  WITH CHECK ADD  CONSTRAINT [FK_ItemInventoriesQuant_Stores] FOREIGN KEY([StoreId])
REFERENCES [dbo].[Stores] ([Id])
GO

ALTER TABLE [dbo].[ItemInventoriesQuant] CHECK CONSTRAINT [FK_ItemInventoriesQuant_Stores]
GO

ALTER TABLE [dbo].[ItemInventoriesQuant]  WITH CHECK ADD  CONSTRAINT [FK_ItemInventoriesQuant_Users] FOREIGN KEY([LastUpdateUserId])
REFERENCES [dbo].[Users] ([Id])
GO

ALTER TABLE [dbo].[ItemInventoriesQuant] CHECK CONSTRAINT [FK_ItemInventoriesQuant_Users]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'店舗Id' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventoriesQuant', @level2type=N'COLUMN',@level2name=N'StoreId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'商品コード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventoriesQuant', @level2type=N'COLUMN',@level2name=N'ItemId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'削除フラグ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventoriesQuant', @level2type=N'COLUMN',@level2name=N'DelFlg'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'作成日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventoriesQuant', @level2type=N'COLUMN',@level2name=N'CrDateTime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventoriesQuant', @level2type=N'COLUMN',@level2name=N'UpDateTime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最終更新ユーザ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventoriesQuant', @level2type=N'COLUMN',@level2name=N'LastUpdateUserId'
GO


