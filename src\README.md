# 棚卸結果取込システム (Inventory Import System)

年2回実施される店舗別棚卸結果CSVファイルの取込処理をUIアプリケーションで効率化するシステムです。

## プロジェクト構成

```
src/
├── InventoryImportSystem.sln              # ソリューションファイル
├── InventoryImportApp/                     # メインアプリケーション (Windows Forms)
│   ├── Config/                            # 設定ファイル
│   ├── Controls/                          # ユーザーコントロール
│   ├── Forms/                             # フォーム
│   └── InventoryImportApp.csproj
├── InventoryImportApp.Core/                # ビジネスロジック・共通ライブラリ
│   ├── Models/                            # モデルクラス
│   ├── Services/                          # サービスクラス
│   └── InventoryImportApp.Core.csproj
└── InventoryImportApp.Data/                # データアクセス層
    ├── Context/                           # Entity Framework Context
    └── InventoryImportApp.Data.csproj
```

## 開発環境

- **.NET**: 9.0
- **UI フレームワーク**: Windows Forms
- **IDE**: Visual Studio Code + C# Dev Kit
- **データベース**: SQL Server (YsskKikanデータベース)

## 必要なパッケージ

- Microsoft.EntityFrameworkCore.SqlServer
- CsvHelper
- NLog.Extensions.Logging
- Microsoft.Extensions.Configuration.Json

## ビルド・実行

```bash
# 依存関係の復元
dotnet restore

# ビルド
dotnet build

# 実行 (デバッグモード)
dotnet run --project InventoryImportApp

# リリースビルド
dotnet build -c Release

# 単一実行ファイル生成
dotnet publish -c Release --self-contained -r win-x64 -p:PublishSingleFile=true
```

## 主な機能

1. **ファイル選択**: 親ディレクトリから店舗別CSVファイルの検索・選択
2. **データインポート**: 選択されたCSVファイルの統合ワークテーブルへの取込
3. **データ補完**: 在庫0商品の補完、売価情報の設定
4. **データ確認**: 統合データのグリッド表示・確認機能
5. **本番登録**: ItemInventoryテーブルへの最終登録

## 設定ファイル

`Config/appsettings.json` で以下の設定が可能：

- **DefaultParentFolder**: デフォルトの親フォルダパス
- **TargetStores**: 対象店舗コード一覧
- **DatabaseConnectionString**: データベース接続文字列
- **UI設定**: ウィンドウタイトル、グリッドページサイズ等
- **Import設定**: ファイルサイズ制限、文字エンコーディング等

## 開発者向け情報

### VS Code拡張機能

- C# Dev Kit
- .NET Extension Pack
- SQL Server (mssql)

### デバッグ

F5キーでデバッグ実行が可能です。launch.jsonとtasks.jsonは自動生成されます。

### コードスタイル

- C# コーディング規約に準拠
- 日本語コメント推奨
- XMLドキュメントコメント記載

## ライセンス

社内利用のみ 