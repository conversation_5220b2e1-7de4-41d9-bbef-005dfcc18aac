namespace InventoryImportApp.Core.Models;

/// <summary>
/// ItemInventories_wkテーブルのデータ統計情報を表すクラス
/// </summary>
public class WorkDataSummary
{
    /// <summary>
    /// 総レコード数
    /// </summary>
    public int TotalRecords { get; set; }
    
    /// <summary>
    /// 店舗数
    /// </summary>
    public int StoreCount { get; set; }
    
    /// <summary>
    /// 商品数
    /// </summary>
    public int ItemCount { get; set; }
    
    /// <summary>
    /// 部門数
    /// </summary>
    public int SectionCount { get; set; }
    
    /// <summary>
    /// 棚卸日
    /// </summary>
    public DateTime? InventoryDate { get; set; }
    
    /// <summary>
    /// 在庫数の合計
    /// </summary>
    public long TotalInventoryCount { get; set; }
    
    /// <summary>
    /// 在庫0の商品数
    /// </summary>
    public int ZeroInventoryItemCount { get; set; }
    
    /// <summary>
    /// 最小在庫数
    /// </summary>
    public int MinInventoryCount { get; set; }
    
    /// <summary>
    /// 最大在庫数
    /// </summary>
    public int MaxInventoryCount { get; set; }
    
    /// <summary>
    /// データ作成日時の範囲（最小）
    /// </summary>
    public DateTime? MinCreateDateTime { get; set; }
    
    /// <summary>
    /// データ作成日時の範囲（最大）
    /// </summary>
    public DateTime? MaxCreateDateTime { get; set; }
}
