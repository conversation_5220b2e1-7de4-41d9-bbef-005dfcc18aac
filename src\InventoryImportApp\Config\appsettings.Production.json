{"InventoryImportApp": {"DatabaseConnectionString": "Data Source=***********;Initial Catalog=YsskKikan;User ID=sa;Password=**********;Connection Timeout=30;Command Timeout=300;TrustServerCertificate=True;Encrypt=False;MultipleActiveResultSets=True;", "DefaultParentFolder": "C:\\YSInventoryUpdate\\out", "UI": {"ConfirmationRequired": true}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning"}, "File": {"LogFilePath": "D:\\Logs\\InventoryImport\\inventory-import-{Date}.log"}}}