USE [YsskKikan]
GO

/****** Object:  Table [dbo].[ItemInventories]    Script Date: 2025/07/29 16:52:52 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[ItemInventories](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[InventoryDate] [date] NOT NULL,
	[ItemId] [bigint] NOT NULL,
	[StoreId] [bigint] NOT NULL,
	[SectionId] [bigint] NOT NULL,
	[InventoryCount] [int] NULL,
	[UnitPrice] [int] NULL,
	[Price] [int] NULL,
	[UnitPriceTaxIn] [int] NULL,
	[PriceTaxIn] [int] NULL,
	[DelFlg] [bigint] NOT NULL,
	[CrDateTime] [datetime] NOT NULL,
	[UpDateTime] [datetime] NOT NULL,
	[LastUpdateUserId] [bigint] NOT NULL,
 CONSTRAINT [PK_ItemInventories] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ItemInventories] ADD  CONSTRAINT [DF_ItemInventories_InventoryDate]  DEFAULT (getdate()) FOR [InventoryDate]
GO

ALTER TABLE [dbo].[ItemInventories] ADD  CONSTRAINT [DF_ItemInventories_ItemId]  DEFAULT ((0)) FOR [ItemId]
GO

ALTER TABLE [dbo].[ItemInventories] ADD  CONSTRAINT [DF_ItemInventories_StoreId]  DEFAULT ((0)) FOR [StoreId]
GO

ALTER TABLE [dbo].[ItemInventories] ADD  CONSTRAINT [DF_ItemInventories_InventoryCount]  DEFAULT ((0)) FOR [InventoryCount]
GO

ALTER TABLE [dbo].[ItemInventories] ADD  CONSTRAINT [DF_ItemInventories_UnitPrice]  DEFAULT ((0)) FOR [UnitPrice]
GO

ALTER TABLE [dbo].[ItemInventories] ADD  CONSTRAINT [DF_ItemInventories_Price]  DEFAULT ((0)) FOR [Price]
GO

ALTER TABLE [dbo].[ItemInventories] ADD  CONSTRAINT [DF_ItemInventories_UnitPriceTaxIn]  DEFAULT ((0)) FOR [UnitPriceTaxIn]
GO

ALTER TABLE [dbo].[ItemInventories] ADD  CONSTRAINT [DF_ItemInventories_PriceTaxIn]  DEFAULT ((0)) FOR [PriceTaxIn]
GO

ALTER TABLE [dbo].[ItemInventories] ADD  CONSTRAINT [DF_ItemInventories_DelFlg]  DEFAULT ((0)) FOR [DelFlg]
GO

ALTER TABLE [dbo].[ItemInventories] ADD  CONSTRAINT [DF_ItemInventories_CrDateTime]  DEFAULT (getdate()) FOR [CrDateTime]
GO

ALTER TABLE [dbo].[ItemInventories] ADD  CONSTRAINT [DF_ItemInventories_UpDateTime]  DEFAULT (getdate()) FOR [UpDateTime]
GO

ALTER TABLE [dbo].[ItemInventories] ADD  CONSTRAINT [DF_ItemInventories_LastUpdateUserId]  DEFAULT ((0)) FOR [LastUpdateUserId]
GO

ALTER TABLE [dbo].[ItemInventories]  WITH CHECK ADD  CONSTRAINT [FK_ItemInventories_Items] FOREIGN KEY([ItemId])
REFERENCES [dbo].[Items] ([Id])
GO

ALTER TABLE [dbo].[ItemInventories] CHECK CONSTRAINT [FK_ItemInventories_Items]
GO

ALTER TABLE [dbo].[ItemInventories]  WITH CHECK ADD  CONSTRAINT [FK_ItemInventories_Sections] FOREIGN KEY([SectionId])
REFERENCES [dbo].[Sections] ([Id])
GO

ALTER TABLE [dbo].[ItemInventories] CHECK CONSTRAINT [FK_ItemInventories_Sections]
GO

ALTER TABLE [dbo].[ItemInventories]  WITH CHECK ADD  CONSTRAINT [FK_ItemInventories_Stores] FOREIGN KEY([StoreId])
REFERENCES [dbo].[Stores] ([Id])
GO

ALTER TABLE [dbo].[ItemInventories] CHECK CONSTRAINT [FK_ItemInventories_Stores]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'棚卸日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventories', @level2type=N'COLUMN',@level2name=N'InventoryDate'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'商品Id' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventories', @level2type=N'COLUMN',@level2name=N'ItemId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'店舗Id' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventories', @level2type=N'COLUMN',@level2name=N'StoreId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部門Id(課,CodeLevel=0)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventories', @level2type=N'COLUMN',@level2name=N'SectionId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'棚卸数(PDA入力値)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventories', @level2type=N'COLUMN',@level2name=N'InventoryCount'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'税抜売単価' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventories', @level2type=N'COLUMN',@level2name=N'UnitPrice'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'税抜売価金額' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventories', @level2type=N'COLUMN',@level2name=N'Price'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'税込売単価' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventories', @level2type=N'COLUMN',@level2name=N'UnitPriceTaxIn'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'税込売価金額' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventories', @level2type=N'COLUMN',@level2name=N'PriceTaxIn'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'削除フラグ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventories', @level2type=N'COLUMN',@level2name=N'DelFlg'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'作成日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventories', @level2type=N'COLUMN',@level2name=N'CrDateTime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventories', @level2type=N'COLUMN',@level2name=N'UpDateTime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最終更新ユーザ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventories', @level2type=N'COLUMN',@level2name=N'LastUpdateUserId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'単品棚卸' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'ItemInventories'
GO


