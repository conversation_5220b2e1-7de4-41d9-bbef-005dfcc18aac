{"version": 3, "targets": {"net9.0": {"Azure.Core/1.44.1": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.1.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "6.0.0", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "6.0.10", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Azure.Core.dll": {"related": ".xml"}}}, "Azure.Identity/1.13.2": {"type": "package", "dependencies": {"Azure.Core": "1.44.1", "Microsoft.Identity.Client": "4.67.2", "Microsoft.Identity.Client.Extensions.Msal": "4.67.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net8.0/Azure.Identity.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Azure.Identity.dll": {"related": ".xml"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}}, "Microsoft.Bcl.Cryptography/9.0.4": {"type": "package", "compile": {"lib/net9.0/Microsoft.Bcl.Cryptography.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Bcl.Cryptography.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Data.SqlClient/6.1.0": {"type": "package", "dependencies": {"Azure.Identity": "1.13.2", "Microsoft.Bcl.Cryptography": "9.0.4", "Microsoft.Data.SqlClient.SNI.runtime": "6.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.4", "Microsoft.IdentityModel.JsonWebTokens": "7.7.1", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.7.1", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "9.0.4", "System.Security.Cryptography.Pkcs": "9.0.4", "System.Text.Json": "9.0.5"}, "compile": {"ref/net9.0/Microsoft.Data.SqlClient.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Data.SqlClient.dll": {"related": ".xml"}}, "resource": {"lib/net9.0/cs/Microsoft.Data.SqlClient.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.Data.SqlClient.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.Data.SqlClient.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net9.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net9.0/Microsoft.Data.SqlClient.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"type": "package", "runtimeTargets": {"runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/_._": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Caching.Memory/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/_._": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"type": "package", "compile": {"lib/net9.0/_._": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "compile": {"lib/net9.0/_._": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/9.0.4": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "compile": {"lib/net9.0/_._": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/9.0.4": {"type": "package", "compile": {"lib/net9.0/_._": {"related": ".xml"}}, "runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "Microsoft.Identity.Client/4.67.2": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "compile": {"lib/net8.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.dll": {"related": ".xml"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.67.2": {"type": "package", "dependencies": {"Microsoft.Identity.Client": "4.67.2", "System.Security.Cryptography.ProtectedData": "4.5.0"}, "compile": {"lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Abstractions/7.7.1": {"type": "package", "compile": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.7.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.7.1"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Logging/7.7.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Abstractions": "7.7.1"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols/7.7.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Tokens": "7.7.1"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.7.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Protocols": "7.7.1", "System.IdentityModel.Tokens.Jwt": "7.7.1"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"related": ".xml"}}}, "Microsoft.IdentityModel.Tokens/7.7.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.Logging": "7.7.1"}, "compile": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"related": ".xml"}}}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"related": ".pdb;.xml"}}}, "NLog/6.0.2": {"type": "package", "compile": {"lib/netstandard2.1/NLog.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.1/NLog.dll": {"related": ".xml"}}}, "System.ClientModel/1.1.0": {"type": "package", "dependencies": {"System.Memory.Data": "1.0.2", "System.Text.Json": "6.0.9"}, "compile": {"lib/net6.0/System.ClientModel.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"related": ".xml"}}}, "System.Configuration.ConfigurationManager/9.0.4": {"type": "package", "dependencies": {"System.Diagnostics.EventLog": "9.0.4", "System.Security.Cryptography.ProtectedData": "9.0.4"}, "compile": {"lib/net9.0/_._": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Diagnostics.DiagnosticSource.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Diagnostics.EventLog/9.0.4": {"type": "package", "compile": {"lib/net9.0/_._": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll": {"assetType": "runtime", "rid": "win"}, "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IdentityModel.Tokens.Jwt/7.7.1": {"type": "package", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.7.1", "Microsoft.IdentityModel.Tokens": "7.7.1"}, "compile": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"related": ".xml"}}}, "System.Memory/4.5.5": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Memory.Data/6.0.0": {"type": "package", "dependencies": {"System.Text.Json": "6.0.0"}, "compile": {"lib/net6.0/System.Memory.Data.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Memory.Data.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "compile": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}}, "System.Security.Cryptography.Pkcs/9.0.4": {"type": "package", "compile": {"lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.ProtectedData/9.0.4": {"type": "package", "compile": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/netcoreapp3.1/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/9.0.5": {"type": "package", "compile": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net9.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/System.Text.Json.targets": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}}}, "libraries": {"Azure.Core/1.44.1": {"sha512": "YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "type": "package", "path": "azure.core/1.44.1", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.core.1.44.1.nupkg.sha512", "azure.core.nuspec", "azureicon.png", "lib/net461/Azure.Core.dll", "lib/net461/Azure.Core.xml", "lib/net472/Azure.Core.dll", "lib/net472/Azure.Core.xml", "lib/net6.0/Azure.Core.dll", "lib/net6.0/Azure.Core.xml", "lib/netstandard2.0/Azure.Core.dll", "lib/netstandard2.0/Azure.Core.xml"]}, "Azure.Identity/1.13.2": {"sha512": "CngQVQELdzFmsGSWyGIPIUOCrII7nApMVWxVmJCKQQrWxRXcNquCsZ+njRJRnhFUfD+KMAhpjyRCaceE4EOL6A==", "type": "package", "path": "azure.identity/1.13.2", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "README.md", "azure.identity.1.13.2.nupkg.sha512", "azure.identity.nuspec", "azureicon.png", "lib/net8.0/Azure.Identity.dll", "lib/net8.0/Azure.Identity.xml", "lib/netstandard2.0/Azure.Identity.dll", "lib/netstandard2.0/Azure.Identity.xml"]}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"sha512": "UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net461/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Bcl.Cryptography/9.0.4": {"sha512": "YgZYAWzyNuPVtPq6WNm0bqOWNjYaWgl5mBWTGZyNoXitYBUYSp6iUB9AwK0V1mo793qRJUXz2t6UZrWITZSvuQ==", "type": "package", "path": "microsoft.bcl.cryptography/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.Cryptography.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Bcl.Cryptography.targets", "lib/net462/Microsoft.Bcl.Cryptography.dll", "lib/net462/Microsoft.Bcl.Cryptography.xml", "lib/net8.0/Microsoft.Bcl.Cryptography.dll", "lib/net8.0/Microsoft.Bcl.Cryptography.xml", "lib/net9.0/Microsoft.Bcl.Cryptography.dll", "lib/net9.0/Microsoft.Bcl.Cryptography.xml", "lib/netstandard2.0/Microsoft.Bcl.Cryptography.dll", "lib/netstandard2.0/Microsoft.Bcl.Cryptography.xml", "microsoft.bcl.cryptography.9.0.4.nupkg.sha512", "microsoft.bcl.cryptography.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Data.SqlClient/6.1.0": {"sha512": "xLUg2ZKmtYiZc2NhOhkXzL6jm28RU1eNGjms5QX5GecRjmNnqdt7F2zWjlbeSqngkODrvdFiQdbF7nHG8x3/5A==", "type": "package", "path": "microsoft.data.sqlclient/6.1.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "dotnet.png", "lib/net462/Microsoft.Data.SqlClient.dll", "lib/net462/Microsoft.Data.SqlClient.xml", "lib/net462/cs/Microsoft.Data.SqlClient.resources.dll", "lib/net462/de/Microsoft.Data.SqlClient.resources.dll", "lib/net462/es/Microsoft.Data.SqlClient.resources.dll", "lib/net462/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net462/it/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net462/pl/Microsoft.Data.SqlClient.resources.dll", "lib/net462/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net462/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net462/tr/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-<PERSON>/Microsoft.Data.SqlClient.resources.dll", "lib/net462/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/Microsoft.Data.SqlClient.dll", "lib/net8.0/Microsoft.Data.SqlClient.xml", "lib/net8.0/cs/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/de/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/es/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/it/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/pl/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/tr/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll", "lib/net8.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/Microsoft.Data.SqlClient.dll", "lib/net9.0/Microsoft.Data.SqlClient.xml", "lib/net9.0/cs/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/de/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/es/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/fr/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/it/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/ja/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/ko/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/pl/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/pt-BR/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/ru/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/tr/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll", "lib/net9.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll", "lib/netstandard2.0/Microsoft.Data.SqlClient.dll", "lib/netstandard2.0/Microsoft.Data.SqlClient.xml", "microsoft.data.sqlclient.6.1.0.nupkg.sha512", "microsoft.data.sqlclient.nuspec", "ref/net462/Microsoft.Data.SqlClient.dll", "ref/net462/Microsoft.Data.SqlClient.xml", "ref/net8.0/Microsoft.Data.SqlClient.dll", "ref/net8.0/Microsoft.Data.SqlClient.xml", "ref/net9.0/Microsoft.Data.SqlClient.dll", "ref/net9.0/Microsoft.Data.SqlClient.xml", "ref/netstandard2.0/Microsoft.Data.SqlClient.dll", "ref/netstandard2.0/Microsoft.Data.SqlClient.xml", "runtimes/unix/lib/net8.0/Microsoft.Data.SqlClient.dll", "runtimes/unix/lib/net9.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net462/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net8.0/Microsoft.Data.SqlClient.dll", "runtimes/win/lib/net9.0/Microsoft.Data.SqlClient.dll"]}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"sha512": "f+pRODTWX7Y67jXO3T5S2dIPZ9qMJNySjlZT/TKmWVNWe19N8jcWmHaqHnnchaq3gxEKv1SWVY5EFzOD06l41w==", "type": "package", "path": "microsoft.data.sqlclient.sni.runtime/6.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "dotnet.png", "microsoft.data.sqlclient.sni.runtime.6.0.2.nupkg.sha512", "microsoft.data.sqlclient.sni.runtime.nuspec", "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll", "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll"]}, "Microsoft.Extensions.Caching.Abstractions/9.0.4": {"sha512": "imcZ5BGhBw5mNsWLepBbqqumWaFe0GtvyCvne2/2wsDIBRa2+Lhx4cU/pKt/4BwOizzUEOls2k1eOJQXHGMalg==", "type": "package", "path": "microsoft.extensions.caching.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Abstractions.targets", "lib/net462/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net462/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Caching.Memory/9.0.4": {"sha512": "G5rEq1Qez5VJDTEyRsRUnewAspKjaY57VGsdZ8g8Ja6sXXzoiI3PpTd1t43HjHqNWD5A06MQveb2lscn+2CU+w==", "type": "package", "path": "microsoft.extensions.caching.memory/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Caching.Memory.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Caching.Memory.targets", "lib/net462/Microsoft.Extensions.Caching.Memory.dll", "lib/net462/Microsoft.Extensions.Caching.Memory.xml", "lib/net8.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net8.0/Microsoft.Extensions.Caching.Memory.xml", "lib/net9.0/Microsoft.Extensions.Caching.Memory.dll", "lib/net9.0/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.9.0.4.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"sha512": "UI0TQPVkS78bFdjkTodmkH0Fe8lXv9LnhGFKgKrsgUJ5a5FVdFRcgjIkBVLbGgdRhxWirxH/8IXUtEyYJx6GQg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"sha512": "0MXlimU4Dud6t+iNi5NEz3dO2w1HXdhoOLaYFuLPCjAsvlPQGwOT6V2KZRMLEhCAm/stSZt1AUv0XmDdkjvtbw==", "type": "package", "path": "microsoft.extensions.logging.abstractions/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net8.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net9.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.9.0.4.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/9.0.4": {"sha512": "fiFI2+58kicqVZyt/6obqoFwHiab7LC4FkQ3mmiBJ28Yy4fAvy2+v9MRnSvvlOO8chTOjKsdafFl/K9veCPo5g==", "type": "package", "path": "microsoft.extensions.options/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net8.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/net9.0/Microsoft.Extensions.Options.dll", "lib/net9.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.9.0.4.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/9.0.4": {"sha512": "SPFyMjyku1nqTFFJ928JAMd0QnRe4xjE7KeKnZMWXf3xk+6e0WiOZAluYtLdbJUXtsl2cCRSi8cBquJ408k8RA==", "type": "package", "path": "microsoft.extensions.primitives/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/net9.0/Microsoft.Extensions.Primitives.dll", "lib/net9.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.9.0.4.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Identity.Client/4.67.2": {"sha512": "37t0TfekfG6XM8kue/xNaA66Qjtti5Qe1xA41CK+bEd8VD76/oXJc+meFJHGzygIC485dCpKoamG/pDfb9Qd7Q==", "type": "package", "path": "microsoft.identity.client/4.67.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/Microsoft.Identity.Client.dll", "lib/net462/Microsoft.Identity.Client.xml", "lib/net472/Microsoft.Identity.Client.dll", "lib/net472/Microsoft.Identity.Client.xml", "lib/net8.0-android34.0/Microsoft.Identity.Client.aar", "lib/net8.0-android34.0/Microsoft.Identity.Client.dll", "lib/net8.0-android34.0/Microsoft.Identity.Client.xml", "lib/net8.0-ios18.0/Microsoft.Identity.Client.dll", "lib/net8.0-ios18.0/Microsoft.Identity.Client.xml", "lib/net8.0/Microsoft.Identity.Client.dll", "lib/net8.0/Microsoft.Identity.Client.xml", "lib/netstandard2.0/Microsoft.Identity.Client.dll", "lib/netstandard2.0/Microsoft.Identity.Client.xml", "microsoft.identity.client.4.67.2.nupkg.sha512", "microsoft.identity.client.nuspec"]}, "Microsoft.Identity.Client.Extensions.Msal/4.67.2": {"sha512": "DKs+Lva6csEUZabw+JkkjtFgVmcXh4pJeQy5KH5XzPOaKNoZhAMYj1qpKd97qYTZKXIFH12bHPk0DA+6krw+Cw==", "type": "package", "path": "microsoft.identity.client.extensions.msal/4.67.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.xml", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.dll", "lib/netstandard2.0/Microsoft.Identity.Client.Extensions.Msal.xml", "microsoft.identity.client.extensions.msal.4.67.2.nupkg.sha512", "microsoft.identity.client.extensions.msal.nuspec"]}, "Microsoft.IdentityModel.Abstractions/7.7.1": {"sha512": "S7sHg6gLg7oFqNGLwN1qSbJDI+QcRRj8SuJ1jHyCmKSipnF6ZQL+tFV2NzVfGj/xmGT9TykQdQiBN+p5Idl4TA==", "type": "package", "path": "microsoft.identitymodel.abstractions/7.7.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Abstractions.dll", "lib/net461/Microsoft.IdentityModel.Abstractions.xml", "lib/net462/Microsoft.IdentityModel.Abstractions.dll", "lib/net462/Microsoft.IdentityModel.Abstractions.xml", "lib/net472/Microsoft.IdentityModel.Abstractions.dll", "lib/net472/Microsoft.IdentityModel.Abstractions.xml", "lib/net6.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net6.0/Microsoft.IdentityModel.Abstractions.xml", "lib/net8.0/Microsoft.IdentityModel.Abstractions.dll", "lib/net8.0/Microsoft.IdentityModel.Abstractions.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Abstractions.xml", "microsoft.identitymodel.abstractions.7.7.1.nupkg.sha512", "microsoft.identitymodel.abstractions.nuspec"]}, "Microsoft.IdentityModel.JsonWebTokens/7.7.1": {"sha512": "3Izi75UCUssvo8LPx3OVnEeZay58qaFicrtSnbtUt7q8qQi0gy46gh4V8VUTkMVMKXV6VMyjBVmeNNgeCUJuIw==", "type": "package", "path": "microsoft.identitymodel.jsonwebtokens/7.7.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net461/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net462/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net472/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.xml", "microsoft.identitymodel.jsonwebtokens.7.7.1.nupkg.sha512", "microsoft.identitymodel.jsonwebtokens.nuspec"]}, "Microsoft.IdentityModel.Logging/7.7.1": {"sha512": "BZNgSq/o8gsKExdYoBKPR65fdsxW0cTF8PsdqB8y011AGUJJW300S/ZIsEUD0+sOmGc003Gwv3FYbjrVjvsLNQ==", "type": "package", "path": "microsoft.identitymodel.logging/7.7.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Logging.dll", "lib/net461/Microsoft.IdentityModel.Logging.xml", "lib/net462/Microsoft.IdentityModel.Logging.dll", "lib/net462/Microsoft.IdentityModel.Logging.xml", "lib/net472/Microsoft.IdentityModel.Logging.dll", "lib/net472/Microsoft.IdentityModel.Logging.xml", "lib/net6.0/Microsoft.IdentityModel.Logging.dll", "lib/net6.0/Microsoft.IdentityModel.Logging.xml", "lib/net8.0/Microsoft.IdentityModel.Logging.dll", "lib/net8.0/Microsoft.IdentityModel.Logging.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Logging.xml", "microsoft.identitymodel.logging.7.7.1.nupkg.sha512", "microsoft.identitymodel.logging.nuspec"]}, "Microsoft.IdentityModel.Protocols/7.7.1": {"sha512": "h+fHHBGokepmCX+QZXJk4Ij8OApCb2n2ktoDkNX5CXteXsOxTHMNgjPGpAwdJMFvAL7TtGarUnk3o97NmBq2QQ==", "type": "package", "path": "microsoft.identitymodel.protocols/7.7.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Protocols.dll", "lib/net461/Microsoft.IdentityModel.Protocols.xml", "lib/net462/Microsoft.IdentityModel.Protocols.dll", "lib/net462/Microsoft.IdentityModel.Protocols.xml", "lib/net472/Microsoft.IdentityModel.Protocols.dll", "lib/net472/Microsoft.IdentityModel.Protocols.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.xml", "microsoft.identitymodel.protocols.7.7.1.nupkg.sha512", "microsoft.identitymodel.protocols.nuspec"]}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.7.1": {"sha512": "yT2Hdj8LpPbcT9C9KlLVxXl09C8zjFaVSaApdOwuecMuoV4s6Sof/mnTDz/+F/lILPIBvrWugR9CC7iRVZgbfQ==", "type": "package", "path": "microsoft.identitymodel.protocols.openidconnect/7.7.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net461/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net462/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net472/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.xml", "microsoft.identitymodel.protocols.openidconnect.7.7.1.nupkg.sha512", "microsoft.identitymodel.protocols.openidconnect.nuspec"]}, "Microsoft.IdentityModel.Tokens/7.7.1": {"sha512": "fQ0VVCba75lknUHGldi3iTKAYUQqbzp1Un8+d9cm9nON0Gs8NAkXddNg8iaUB0qi/ybtAmNWizTR4avdkCJ9pQ==", "type": "package", "path": "microsoft.identitymodel.tokens/7.7.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/Microsoft.IdentityModel.Tokens.dll", "lib/net461/Microsoft.IdentityModel.Tokens.xml", "lib/net462/Microsoft.IdentityModel.Tokens.dll", "lib/net462/Microsoft.IdentityModel.Tokens.xml", "lib/net472/Microsoft.IdentityModel.Tokens.dll", "lib/net472/Microsoft.IdentityModel.Tokens.xml", "lib/net6.0/Microsoft.IdentityModel.Tokens.dll", "lib/net6.0/Microsoft.IdentityModel.Tokens.xml", "lib/net8.0/Microsoft.IdentityModel.Tokens.dll", "lib/net8.0/Microsoft.IdentityModel.Tokens.xml", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll", "lib/netstandard2.0/Microsoft.IdentityModel.Tokens.xml", "microsoft.identitymodel.tokens.7.7.1.nupkg.sha512", "microsoft.identitymodel.tokens.nuspec"]}, "Microsoft.SqlServer.Server/1.0.0": {"sha512": "N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "type": "package", "path": "microsoft.sqlserver.server/1.0.0", "files": [".nupkg.metadata", ".signature.p7s", "dotnet.png", "lib/net46/Microsoft.SqlServer.Server.dll", "lib/net46/Microsoft.SqlServer.Server.pdb", "lib/net46/Microsoft.SqlServer.Server.xml", "lib/netstandard2.0/Microsoft.SqlServer.Server.dll", "lib/netstandard2.0/Microsoft.SqlServer.Server.pdb", "lib/netstandard2.0/Microsoft.SqlServer.Server.xml", "microsoft.sqlserver.server.1.0.0.nupkg.sha512", "microsoft.sqlserver.server.nuspec"]}, "NLog/6.0.2": {"sha512": "kfjfjcjh/hHXLJ0TbEUH6ajb2jQFmwk/23nyYW9iPZ6cj5769SyeDTbUwGI7LSVUk5iTRJoC6CTKKmWrXK79oA==", "type": "package", "path": "nlog/6.0.2", "files": [".nupkg.metadata", ".signature.p7s", "N.png", "lib/net35/NLog.dll", "lib/net35/NLog.xml", "lib/net45/NLog.dll", "lib/net45/NLog.xml", "lib/net46/NLog.dll", "lib/net46/NLog.xml", "lib/netstandard2.0/NLog.dll", "lib/netstandard2.0/NLog.xml", "lib/netstandard2.1/NLog.dll", "lib/netstandard2.1/NLog.xml", "nlog.6.0.2.nupkg.sha512", "nlog.nuspec"]}, "System.ClientModel/1.1.0": {"sha512": "UocOlCkxLZrG2CKMAAImPcldJTxeesHnHGHwhJ0pNlZEvEXcWKuQvVOER2/NiOkJGRJk978SNdw3j6/7O9H1lg==", "type": "package", "path": "system.clientmodel/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "CHANGELOG.md", "DotNetPackageIcon.png", "README.md", "lib/net6.0/System.ClientModel.dll", "lib/net6.0/System.ClientModel.xml", "lib/netstandard2.0/System.ClientModel.dll", "lib/netstandard2.0/System.ClientModel.xml", "system.clientmodel.1.1.0.nupkg.sha512", "system.clientmodel.nuspec"]}, "System.Configuration.ConfigurationManager/9.0.4": {"sha512": "dvjqKp+2LpGid6phzrdrS/2mmEPxFl3jE1+L7614q4ZChKbLJCpHXg6sBILlCCED1t//EE+un/UdAetzIMpqnw==", "type": "package", "path": "system.configuration.configurationmanager/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net8.0/System.Configuration.ConfigurationManager.dll", "lib/net8.0/System.Configuration.ConfigurationManager.xml", "lib/net9.0/System.Configuration.ConfigurationManager.dll", "lib/net9.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.9.0.4.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.DiagnosticSource/6.0.1": {"sha512": "KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Diagnostics.DiagnosticSource.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Diagnostics.DiagnosticSource.dll", "lib/net461/System.Diagnostics.DiagnosticSource.xml", "lib/net5.0/System.Diagnostics.DiagnosticSource.dll", "lib/net5.0/System.Diagnostics.DiagnosticSource.xml", "lib/net6.0/System.Diagnostics.DiagnosticSource.dll", "lib/net6.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard2.0/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt"]}, "System.Diagnostics.EventLog/9.0.4": {"sha512": "getRQEXD8idlpb1KW56XuxImMy0FKp2WJPDf3Qr0kI/QKxxJSftqfDFVo0DZ3HCJRLU73qHSruv5q2l5O47jQQ==", "type": "package", "path": "system.diagnostics.eventlog/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Diagnostics.EventLog.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Diagnostics.EventLog.targets", "lib/net462/System.Diagnostics.EventLog.dll", "lib/net462/System.Diagnostics.EventLog.xml", "lib/net8.0/System.Diagnostics.EventLog.dll", "lib/net8.0/System.Diagnostics.EventLog.xml", "lib/net9.0/System.Diagnostics.EventLog.dll", "lib/net9.0/System.Diagnostics.EventLog.xml", "lib/netstandard2.0/System.Diagnostics.EventLog.dll", "lib/netstandard2.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.xml", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll", "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.xml", "system.diagnostics.eventlog.9.0.4.nupkg.sha512", "system.diagnostics.eventlog.nuspec", "useSharedDesignerContext.txt"]}, "System.IdentityModel.Tokens.Jwt/7.7.1": {"sha512": "rQkO1YbAjLwnDJSMpRhRtrc6XwIcEOcUvoEcge+evurpzSZM3UNK+MZfD3sKyTlYsvknZ6eJjSBfnmXqwOsT9Q==", "type": "package", "path": "system.identitymodel.tokens.jwt/7.7.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net461/System.IdentityModel.Tokens.Jwt.dll", "lib/net461/System.IdentityModel.Tokens.Jwt.xml", "lib/net462/System.IdentityModel.Tokens.Jwt.dll", "lib/net462/System.IdentityModel.Tokens.Jwt.xml", "lib/net472/System.IdentityModel.Tokens.Jwt.dll", "lib/net472/System.IdentityModel.Tokens.Jwt.xml", "lib/net6.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net6.0/System.IdentityModel.Tokens.Jwt.xml", "lib/net8.0/System.IdentityModel.Tokens.Jwt.dll", "lib/net8.0/System.IdentityModel.Tokens.Jwt.xml", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll", "lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.xml", "system.identitymodel.tokens.jwt.7.7.1.nupkg.sha512", "system.identitymodel.tokens.jwt.nuspec"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Memory.Data/6.0.0": {"sha512": "ntFHArH3I4Lpjf5m4DCXQHJuGwWPNVJPaAvM95Jy/u+2Yzt2ryiyIN04LAogkjP9DeRcEOiviAjQotfmPq/FrQ==", "type": "package", "path": "system.memory.data/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Memory.Data.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Memory.Data.dll", "lib/net461/System.Memory.Data.xml", "lib/net6.0/System.Memory.Data.dll", "lib/net6.0/System.Memory.Data.xml", "lib/netstandard2.0/System.Memory.Data.dll", "lib/netstandard2.0/System.Memory.Data.xml", "system.memory.data.6.0.0.nupkg.sha512", "system.memory.data.nuspec", "useSharedDesignerContext.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.Pkcs/9.0.4": {"sha512": "cUFTcMlz/Qw9s90b2wnWSCvHdjv51Bau9FQqhsr4TlwSe1OX+7SoXUqphis5G74MLOvMOCghxPPlEqOdCrVVGA==", "type": "package", "path": "system.security.cryptography.pkcs/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.Pkcs.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.Pkcs.targets", "lib/net462/System.Security.Cryptography.Pkcs.dll", "lib/net462/System.Security.Cryptography.Pkcs.xml", "lib/net8.0/System.Security.Cryptography.Pkcs.dll", "lib/net8.0/System.Security.Cryptography.Pkcs.xml", "lib/net9.0/System.Security.Cryptography.Pkcs.dll", "lib/net9.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.0/System.Security.Cryptography.Pkcs.xml", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.dll", "lib/netstandard2.1/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net8.0/System.Security.Cryptography.Pkcs.xml", "runtimes/win/lib/net9.0/System.Security.Cryptography.Pkcs.dll", "runtimes/win/lib/net9.0/System.Security.Cryptography.Pkcs.xml", "system.security.cryptography.pkcs.9.0.4.nupkg.sha512", "system.security.cryptography.pkcs.nuspec", "useSharedDesignerContext.txt"]}, "System.Security.Cryptography.ProtectedData/9.0.4": {"sha512": "o94k2RKuAce3GeDMlUvIXlhVa1kWpJw95E6C9LwW0KlG0nj5+SgCiIxJ2Eroqb9sLtG1mEMbFttZIBZ13EJPvQ==", "type": "package", "path": "system.security.cryptography.protecteddata/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Security.Cryptography.ProtectedData.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Security.Cryptography.ProtectedData.targets", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net462/System.Security.Cryptography.ProtectedData.dll", "lib/net462/System.Security.Cryptography.ProtectedData.xml", "lib/net8.0/System.Security.Cryptography.ProtectedData.dll", "lib/net8.0/System.Security.Cryptography.ProtectedData.xml", "lib/net9.0/System.Security.Cryptography.ProtectedData.dll", "lib/net9.0/System.Security.Cryptography.ProtectedData.xml", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll", "lib/netstandard2.0/System.Security.Cryptography.ProtectedData.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "system.security.cryptography.protecteddata.9.0.4.nupkg.sha512", "system.security.cryptography.protecteddata.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Encodings.Web/6.0.0": {"sha512": "Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "type": "package", "path": "system.text.encodings.web/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Text.Encodings.Web.dll", "lib/net461/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/netcoreapp3.1/System.Text.Encodings.Web.dll", "lib/netcoreapp3.1/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.6.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/9.0.5": {"sha512": "rnP61ZfloTgPQPe7ecr36loNiGX3g1PocxlKHdY/FUpDSsExKkTxpMAlB4X35wNEPr1X7mkYZuQvW3Lhxmu7KA==", "type": "package", "path": "system.text.json/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net8.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/net9.0/System.Text.Json.dll", "lib/net9.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.9.0.5.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {"net9.0": ["Microsoft.Data.SqlClient >= 6.1.0", "NLog >= 6.0.2"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\YSInventoryUpdate\\src\\InventoryImportApp.Core\\InventoryImportApp.Core.csproj", "projectName": "InventoryImportApp.Core", "projectPath": "C:\\YSInventoryUpdate\\src\\InventoryImportApp.Core\\InventoryImportApp.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\YSInventoryUpdate\\src\\InventoryImportApp.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.1.0, )"}, "NLog": {"target": "Package", "version": "[6.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}