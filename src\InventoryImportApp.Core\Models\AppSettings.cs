namespace InventoryImportApp.Core.Models;

public class AppSettings
{
    public InventoryImportAppSettings InventoryImportApp { get; set; } = new();
    public LoggingSettings Logging { get; set; } = new();
}

public class InventoryImportAppSettings
{
    public string DefaultParentFolder { get; set; } = string.Empty;
    public List<string> TargetStores { get; set; } = new();
    public string DatabaseConnectionString { get; set; } = string.Empty;
    public UISettings UI { get; set; } = new();
    public ImportSettings Import { get; set; } = new();
}

public class UISettings
{
    public string WindowTitle { get; set; } = "棚卸結果取込システム";
    public int GridPageSize { get; set; } = 1000;
    public bool ConfirmationRequired { get; set; } = true;
}

public class ImportSettings
{
    public long MaxFileSize { get; set; } = 52428800; // 50MB
    public List<string> SupportedEncodings { get; set; } = new();
    public List<string> RequiredColumns { get; set; } = new();
}

public class LoggingSettings
{
    public Dictionary<string, string> LogLevel { get; set; } = new();
    public FileLogSettings File { get; set; } = new();
}

public class FileLogSettings
{
    public string LogFilePath { get; set; } = string.Empty;
} 