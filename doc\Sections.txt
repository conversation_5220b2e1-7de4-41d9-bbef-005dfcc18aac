USE [YsskKikan]
GO

/****** Object:  Table [dbo].[Sections]    Script Date: 2025/07/30 17:41:13 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Sections](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[CodeLevel] [int] NOT NULL,
	[CategoryCode] [varchar](4) NOT NULL,
	[CategoryName] [nvarchar](50) NOT NULL,
	[ParentCodeId] [bigint] NOT NULL,
	[DefaultItemId] [bigint] NULL,
	[CostRate] [decimal](18, 2) NULL,
	[PosDeliveryDateTime] [datetime] NULL,
	[DelFlg] [bigint] NOT NULL,
	[CrDateTime] [datetime] NOT NULL,
	[UpDateTime] [datetime] NOT NULL,
	[LastUpdateUserId] [bigint] NOT NULL,
	[ResultsHide] [bit] NOT NULL,
	[TaxDivisionCode] [varchar](50) NOT NULL,
	[ProducingInput] [bit] NOT NULL,
	[AutoOdFlg] [bit] NOT NULL,
	[PointFlg] [bit] NOT NULL,
	[AgelimitFlg] [bit] NOT NULL,
	[AutoInvLossFlg] [bit] NOT NULL,
	[MinCoefRankA] [decimal](5, 2) NULL,
	[MinCoefRankB] [decimal](5, 2) NULL,
	[MinCoefRankC] [decimal](5, 2) NULL,
	[MinCoefRankD] [decimal](5, 2) NULL,
	[MinCoefRankE] [decimal](5, 2) NULL,
	[MaxCoefRankA] [decimal](5, 2) NULL,
	[MaxCoefRankB] [decimal](5, 2) NULL,
	[MaxCoefRankC] [decimal](5, 2) NULL,
	[MaxCoefRankD] [decimal](5, 2) NULL,
	[MaxCoefRankE] [decimal](5, 2) NULL,
	[InventoryCalcFlg] [bit] NOT NULL,
	[DaisoSkuBunrui1Cd] [varchar](10) NULL,
	[TABFlg] [bit] NOT NULL,
	[PreExclusionPeriod] [int] NULL,
 CONSTRAINT [PK_Sections] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[Sections] ADD  CONSTRAINT [DF_Sections_CodeLevel]  DEFAULT ((0)) FOR [CodeLevel]
GO

ALTER TABLE [dbo].[Sections] ADD  CONSTRAINT [DF_Sections_CategoryCode]  DEFAULT ('') FOR [CategoryCode]
GO

ALTER TABLE [dbo].[Sections] ADD  CONSTRAINT [DF_Sections_CategoryName]  DEFAULT ('') FOR [CategoryName]
GO

ALTER TABLE [dbo].[Sections] ADD  CONSTRAINT [DF_Sections_ParentCodeId]  DEFAULT ((0)) FOR [ParentCodeId]
GO

ALTER TABLE [dbo].[Sections] ADD  CONSTRAINT [DF_Sections_DelFlg]  DEFAULT ((0)) FOR [DelFlg]
GO

ALTER TABLE [dbo].[Sections] ADD  CONSTRAINT [DF_Sections_CrDate]  DEFAULT (getdate()) FOR [CrDateTime]
GO

ALTER TABLE [dbo].[Sections] ADD  CONSTRAINT [DF_Sections_UpDate]  DEFAULT (getdate()) FOR [UpDateTime]
GO

ALTER TABLE [dbo].[Sections] ADD  CONSTRAINT [DF_Sections_LastUpdateUser]  DEFAULT ((0)) FOR [LastUpdateUserId]
GO

ALTER TABLE [dbo].[Sections] ADD  CONSTRAINT [DF_Sections_ResultHide]  DEFAULT ((0)) FOR [ResultsHide]
GO

ALTER TABLE [dbo].[Sections] ADD  CONSTRAINT [DF_Sections_TaxDivisionCode]  DEFAULT ('') FOR [TaxDivisionCode]
GO

ALTER TABLE [dbo].[Sections] ADD  CONSTRAINT [DF_Sections_ProducingInput]  DEFAULT ((0)) FOR [ProducingInput]
GO

ALTER TABLE [dbo].[Sections] ADD  CONSTRAINT [DF_Sections_AutoOdFlg]  DEFAULT ((0)) FOR [AutoOdFlg]
GO

ALTER TABLE [dbo].[Sections] ADD  DEFAULT ((0)) FOR [PointFlg]
GO

ALTER TABLE [dbo].[Sections] ADD  CONSTRAINT [DF_Sections_AgelimitFlg]  DEFAULT ((0)) FOR [AgelimitFlg]
GO

ALTER TABLE [dbo].[Sections] ADD  CONSTRAINT [DF_Sections_AutoInvLossFlg]  DEFAULT ((0)) FOR [AutoInvLossFlg]
GO

ALTER TABLE [dbo].[Sections] ADD  DEFAULT ((0)) FOR [InventoryCalcFlg]
GO

ALTER TABLE [dbo].[Sections] ADD  DEFAULT ((0)) FOR [TABFlg]
GO

ALTER TABLE [dbo].[Sections]  WITH CHECK ADD  CONSTRAINT [FK_Sections_Items] FOREIGN KEY([DefaultItemId])
REFERENCES [dbo].[Items] ([Id])
GO

ALTER TABLE [dbo].[Sections] CHECK CONSTRAINT [FK_Sections_Items]
GO

ALTER TABLE [dbo].[Sections]  WITH CHECK ADD  CONSTRAINT [FK_Sections_Users] FOREIGN KEY([LastUpdateUserId])
REFERENCES [dbo].[Users] ([Id])
GO

ALTER TABLE [dbo].[Sections] CHECK CONSTRAINT [FK_Sections_Users]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部門レベル。0:課、1:SDEPT、2:クラス' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Sections', @level2type=N'COLUMN',@level2name=N'CodeLevel'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部門コード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Sections', @level2type=N'COLUMN',@level2name=N'CategoryCode'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部門名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Sections', @level2type=N'COLUMN',@level2name=N'CategoryName'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'親部門Id' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Sections', @level2type=N'COLUMN',@level2name=N'ParentCodeId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'デフォルト商品Id(エラークラス)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Sections', @level2type=N'COLUMN',@level2name=N'DefaultItemId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'原価率' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Sections', @level2type=N'COLUMN',@level2name=N'CostRate'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'削除フラグ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Sections', @level2type=N'COLUMN',@level2name=N'DelFlg'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'作成日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Sections', @level2type=N'COLUMN',@level2name=N'CrDateTime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Sections', @level2type=N'COLUMN',@level2name=N'UpDateTime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最終更新ユーザ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Sections', @level2type=N'COLUMN',@level2name=N'LastUpdateUserId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'部門' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Sections'
GO


