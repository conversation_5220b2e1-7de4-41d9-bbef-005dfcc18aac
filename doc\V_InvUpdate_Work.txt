SELECT                      TOP (100) PERCENT dbo.Stores.Id AS StoreId, dbo.Items.Id AS ItemId, dbo.InventoryAll_wk.[列 3] AS InventoryCount, 
                                      Sections_2.Id AS SectionId, 12 AS LastUpdateUser, 0 AS DelFlg, CONVERT(datetime, CONVERT(CHAR(8), 
                                      dbo.InventoryAll_wk.[列 1]), 120) AS InvDate, CONVERT(DATETIME, '2025-02-28 23:59:59', 120) AS CrDate, 
                                      CONVERT(DATETIME, '2025-02-28 23:59:59', 120) AS UpdDate
FROM                         dbo.InventoryAll_wk INNER JOIN
                                      dbo.Stores ON dbo.InventoryAll_wk.[列 0] = dbo.Stores.StoreCode INNER JOIN
                                      dbo.Items ON dbo.InventoryAll_wk.[列 2] = dbo.Items.ItemCode INNER JOIN
                                      dbo.Sections ON dbo.Items.SectionId = dbo.Sections.Id INNER JOIN
                                      dbo.Sections AS Sections_1 ON dbo.Sections.ParentCodeId = Sections_1.Id INNER JOIN
                                      dbo.Sections AS Sections_2 ON Sections_1.ParentCodeId = Sections_2.Id
WHERE                       (dbo.Items.DelFlg = 0)
ORDER BY               StoreId, ItemId