namespace InventoryImportApp.Core.Models;

/// <summary>
/// インポート処理の結果を表すクラス
/// </summary>
public class ImportResult
{
    /// <summary>
    /// 処理が成功したかどうか
    /// </summary>
    public bool IsSuccess { get; set; }
    
    /// <summary>
    /// エラーメッセージ（失敗時）
    /// </summary>
    public string ErrorMessage { get; set; } = string.Empty;
    
    /// <summary>
    /// インポートしたCSVレコード数
    /// </summary>
    public int ImportedCsvRecords { get; set; }
    
    /// <summary>
    /// 処理されたレコード数（マスタ結合後）
    /// </summary>
    public int ProcessedRecords { get; set; }
    
    /// <summary>
    /// 本番登録されたレコード数（本番登録実装時に使用）
    /// </summary>
    public int RegisteredRecords { get; set; }
    
    /// <summary>
    /// 在庫0商品として補完されたレコード数
    /// </summary>
    public int ZeroInventoryRecords { get; set; }
    
    /// <summary>
    /// 重複により削除されたレコード数
    /// </summary>
    public int DuplicateRemovedRecords { get; set; }
    
    /// <summary>
    /// 処理開始時刻
    /// </summary>
    public DateTime StartTime { get; set; }
    
    /// <summary>
    /// 処理終了時刻
    /// </summary>
    public DateTime EndTime { get; set; }
    
    /// <summary>
    /// 処理時間
    /// </summary>
    public TimeSpan Duration => EndTime - StartTime;
    
    /// <summary>
    /// 成功結果を作成
    /// </summary>
    public static ImportResult Success(int importedCsvRecords, int processedRecords)
    {
        return new ImportResult
        {
            IsSuccess = true,
            ImportedCsvRecords = importedCsvRecords,
            ProcessedRecords = processedRecords,
            EndTime = DateTime.Now
        };
    }
    
    /// <summary>
    /// 失敗結果を作成
    /// </summary>
    public static ImportResult Failure(string errorMessage)
    {
        return new ImportResult
        {
            IsSuccess = false,
            ErrorMessage = errorMessage,
            EndTime = DateTime.Now
        };
    }
}
