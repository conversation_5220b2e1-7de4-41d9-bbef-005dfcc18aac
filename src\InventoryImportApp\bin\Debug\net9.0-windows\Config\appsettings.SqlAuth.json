{"_comment": "SQL Server認証を使用する場合の設定テンプレート", "InventoryImportApp": {"DatabaseConnectionString": "Data Source=***********;Initial Catalog=YsskKikan;User ID=sa;Password=**********;Connection Timeout=30;Command Timeout=300;TrustServerCertificate=True;Encrypt=False;MultipleActiveResultSets=True;", "DefaultParentFolder": "D:\\Inventory\\Input"}, "ConnectionStrings": {"_examples": {"LocalSqlExpress": "Data Source=.\\SQLEXPRESS;Initial Catalog=YsskKikan;Integrated Security=True;", "RemoteServer": "Data Source=*************;Initial Catalog=YsskKikan;User ID=inventory_user;Password=your_password;", "NamedPipe": "Data Source=np:ServerName\\InstanceName;Initial Catalog=YsskKikan;Integrated Security=True;"}}}