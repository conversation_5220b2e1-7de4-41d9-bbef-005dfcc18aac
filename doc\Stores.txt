USE [Yssk<PERSON><PERSON>n]
GO

/****** Object:  Table [dbo].[Stores]    Script Date: 2025/07/30 17:40:05 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Stores](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[StoreCode] [varchar](3) NOT NULL,
	[StoreName] [nvarchar](50) NOT NULL,
	[PosTypeId] [bigint] NULL,
	[PointCtrlTypeId] [bigint] NULL,
	[ActiveFrom] [date] NULL,
	[ActiveTo] [date] NULL,
	[Address1] [nvarchar](50) NOT NULL,
	[Address2] [nvarchar](50) NOT NULL,
	[TelNo] [varchar](50) NOT NULL,
	[FaxNo] [varchar](50) NOT NULL,
	[NonActive] [bit] NOT NULL,
	[PosStoreCode] [varchar](50) NULL,
	[RegiNoFrom] [int] NULL,
	[RegiNoTo] [int] NULL,
	[FloorNo] [int] NULL,
	[DelFlg] [bigint] NOT NULL,
	[CrDateTime] [datetime] NOT NULL,
	[UpDateTime] [datetime] NOT NULL,
	[LastUpdateUserId] [bigint] NOT NULL,
	[ShortName] [nvarchar](3) NULL,
	[BuyerOrder] [bit] NOT NULL,
	[TCFlg] [bit] NOT NULL,
	[TCId] [bigint] NULL,
	[AutoOdFlg] [bit] NOT NULL,
	[TstoreId] [varchar](10) NULL,
	[ZipCode] [varchar](7) NULL,
	[MailAddress] [varchar](100) NULL,
	[InventoryCalcFlg] [bit] NOT NULL,
	[IP_SEGMENT] [varchar](12) NULL,
	[AREA_2] [int] NULL,
 CONSTRAINT [PK_Stores] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_StoreCode]  DEFAULT ('') FOR [StoreCode]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_StoreName]  DEFAULT ('') FOR [StoreName]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_Address1]  DEFAULT ('') FOR [Address1]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_address2]  DEFAULT ('') FOR [Address2]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_TelNo]  DEFAULT ('') FOR [TelNo]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_FaxNo]  DEFAULT ('') FOR [FaxNo]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_NonActive]  DEFAULT ((0)) FOR [NonActive]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_PosStoreCode]  DEFAULT ('') FOR [PosStoreCode]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_RegiNoFrom1]  DEFAULT ((0)) FOR [RegiNoFrom]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_RegiNoFrom]  DEFAULT ((0)) FOR [RegiNoTo]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_FloorNo]  DEFAULT ((0)) FOR [FloorNo]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_DelFlg]  DEFAULT ((0)) FOR [DelFlg]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_CrDate]  DEFAULT (getdate()) FOR [CrDateTime]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_Update]  DEFAULT (getdate()) FOR [UpDateTime]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_LastUpdateUser]  DEFAULT ((0)) FOR [LastUpdateUserId]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_BuyerOrder]  DEFAULT ((0)) FOR [BuyerOrder]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_TCFlg]  DEFAULT ((0)) FOR [TCFlg]
GO

ALTER TABLE [dbo].[Stores] ADD  CONSTRAINT [DF_Stores_AutoOdFlg]  DEFAULT ((0)) FOR [AutoOdFlg]
GO

ALTER TABLE [dbo].[Stores] ADD  DEFAULT ((0)) FOR [InventoryCalcFlg]
GO

ALTER TABLE [dbo].[Stores]  WITH CHECK ADD  CONSTRAINT [FK_Stores_GaneralMasterValuesPointCtrlTypeId] FOREIGN KEY([PointCtrlTypeId])
REFERENCES [dbo].[GeneralMasterValues] ([Id])
GO

ALTER TABLE [dbo].[Stores] CHECK CONSTRAINT [FK_Stores_GaneralMasterValuesPointCtrlTypeId]
GO

ALTER TABLE [dbo].[Stores]  WITH CHECK ADD  CONSTRAINT [FK_Stores_GaneralMasterValuesPosTypeId] FOREIGN KEY([PosTypeId])
REFERENCES [dbo].[GeneralMasterValues] ([Id])
GO

ALTER TABLE [dbo].[Stores] CHECK CONSTRAINT [FK_Stores_GaneralMasterValuesPosTypeId]
GO

ALTER TABLE [dbo].[Stores]  WITH CHECK ADD  CONSTRAINT [FK_Stores_Users] FOREIGN KEY([LastUpdateUserId])
REFERENCES [dbo].[Users] ([Id])
GO

ALTER TABLE [dbo].[Stores] CHECK CONSTRAINT [FK_Stores_Users]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'店舗コード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'StoreCode'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'店舗名' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'StoreName'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'GeneralMasters.Code = 3,POS種類' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'PosTypeId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'GeneralMasters.Code = 4,ポイント管理種類' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'PointCtrlTypeId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'開店日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'ActiveFrom'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'閉店日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'ActiveTo'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'住所1' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'Address1'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'住所2' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'Address2'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'電話番号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'TelNo'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'FAX番号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'FaxNo'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'利用停止フラグ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'NonActive'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'POS店舗コード' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'PosStoreCode'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'レジ番号From' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'RegiNoFrom'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'レジ番号To' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'RegiNoTo'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'フロア番号' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'FloorNo'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'削除フラグ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'DelFlg'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'作成日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'CrDateTime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'更新日' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'UpDateTime'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'最終更新ユーザ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'LastUpdateUserId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'T加盟店ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores', @level2type=N'COLUMN',@level2name=N'TstoreId'
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'店舗' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Stores'
GO


