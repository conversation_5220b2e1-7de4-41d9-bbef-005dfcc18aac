/*
【単品棚卸し在庫更新】
①InventoryALL_XXX.csv を　dbo.InventoryALL_XXXとしてインポート。
　※XXXは店舗コード
　※過去に取り込んだ時のテーブルは消しておく。
　※エイジスからのデータを自動発注連携用ファイルに変換したもの。
　※文字修飾「”」としてインポートとしてとりこむ。
　※全店分

②取り込み元のワークテーブルを空にする
*/

use YsskKikan
truncate table dbo.InventoryALL_wk
go

use YsskKikan
truncate table dbo.ItemInventories_wk
go

/*
③ビューの「dbo.V_InvUpdate_Work」の作成日時（CrDateTime）、更新日時（UpDateTime）
　を処理予定日（早朝のバッチ時刻）の前日23:59:59に変更しておく。
　明日朝反映させるには、本日日付。
　V_InvZeroUpdate_Workも同様。

④InventoryALL_wkに店舗棚卸データを集める　処理分の店舗のみ
*/
INSERT INTO  InventoryALL_wk
                        ([列 0], [列 1], [列 2], [列 3])
SELECT            [列 0], [列 1], [列 2], [列 3]
FROM              InventoryALL_347
go

INSERT INTO  InventoryALL_wk
                        ([列 0], [列 1], [列 2], [列 3])
SELECT            [列 0], [列 1], [列 2], [列 3]
FROM              InventoryALL_351
go

INSERT INTO  InventoryALL_wk
                        ([列 0], [列 1], [列 2], [列 3])
SELECT            [列 0], [列 1], [列 2], [列 3]
FROM              InventoryALL_353
go

INSERT INTO  InventoryALL_wk
                        ([列 0], [列 1], [列 2], [列 3])
SELECT            [列 0], [列 1], [列 2], [列 3]
FROM              InventoryALL_355
go

INSERT INTO  InventoryALL_wk
                        ([列 0], [列 1], [列 2], [列 3])
SELECT            [列 0], [列 1], [列 2], [列 3]
FROM              InventoryALL_360
go

INSERT INTO  InventoryALL_wk
                        ([列 0], [列 1], [列 2], [列 3])
SELECT            [列 0], [列 1], [列 2], [列 3]
FROM              InventoryALL_362
go

INSERT INTO  InventoryALL_wk
                        ([列 0], [列 1], [列 2], [列 3])
SELECT            [列 0], [列 1], [列 2], [列 3]
FROM              InventoryALL_363
go

INSERT INTO  InventoryALL_wk
                        ([列 0], [列 1], [列 2], [列 3])
SELECT            [列 0], [列 1], [列 2], [列 3]
FROM              InventoryALL_364
go

INSERT INTO  InventoryALL_wk
                        ([列 0], [列 1], [列 2], [列 3])
SELECT            [列 0], [列 1], [列 2], [列 3]
FROM              InventoryALL_365
go

INSERT INTO  InventoryALL_wk
                        ([列 0], [列 1], [列 2], [列 3])
SELECT            [列 0], [列 1], [列 2], [列 3]
FROM              InventoryALL_366
go

INSERT INTO  InventoryALL_wk
                        ([列 0], [列 1], [列 2], [列 3])
SELECT            [列 0], [列 1], [列 2], [列 3]
FROM              InventoryALL_367
go


INSERT INTO  InventoryALL_wk
                        ([列 0], [列 1], [列 2], [列 3])
SELECT            [列 0], [列 1], [列 2], [列 3]
FROM              InventoryALL_443
go

INSERT INTO  InventoryALL_wk
                        ([列 0], [列 1], [列 2], [列 3])
SELECT            [列 0], [列 1], [列 2], [列 3]
FROM              InventoryALL_444
go

/*
⑤取り込み元のワークテーブルにビューから値を挿入する
*/
INSERT INTO     ItemInventories_wk
                                  (StoreId, ItemId, InventoryCount, SectionId, LastUpdateUserId, DelFlg, InventoryDate, CrDateTime, UpDateTime)
SELECT                  StoreId, ItemId, InventoryCount, SectionId, LastUpdateUser, DelFlg, InvDate, CrDate, UpdDate
FROM                     V_InvUpdate_Work
go






/*
⑥ItemInventories_wkにV_InvZeroUpdate_Workから棚卸無し分（InventoryALL_wkになくQuantに数量のある（0以外）商品）を在庫０として挿入する。
　更新後、V_InvZeroUpdate_Workは結果がなくなる。*/
*/



INSERT INTO  ItemInventories_wk
                        (StoreId, ItemId, SectionId, LastUpdateUserId, DelFlg, CrDateTime, UpDateTime, InventoryDate, InventoryCount)
SELECT            StoreId, ItemId, SectionId, LastUpdateUser, DelFlg, CrDate, UpdDate, InvDate, InventoryCount
FROM              V_InvZeroUpdate_Work
go

/*
⑦処理に使っていないが、売価なども念のため投入しておく
*/
UPDATE                ItemInventories_wk
SET                          UnitPrice = Items.RegularPrice, Price = Items.RegularPrice * ItemInventories_wk.InventoryCount, UnitPriceTaxIn = Items.RegularPriceTaxIn, 
                                  PriceTaxIn = Items.RegularPriceTaxIn * ItemInventories_wk.InventoryCount
FROM                     ItemInventories_wk INNER JOIN Items ON ItemInventories_wk.ItemId = Items.Id
go

/*
⑧定期棚卸日以降に、店舗が棚卸したデータがあれば、ItemInventories_wkから削除しておく。
*/
DELETE FROM ItemInventories_wk
FROM              ItemInventories_wk INNER JOIN
                        ItemInventories ON ItemInventories_wk.ItemId = ItemInventories.ItemId AND ItemInventories_wk.StoreId = ItemInventories.StoreId AND 
                        ItemInventories_wk.InventoryDate <= ItemInventories.InventoryDate

/*
⑨念のため、全店そろったか確認
*/
SELECT                  TOP (200) InventoryDate, StoreId, CrDateTime, UpDateTime
FROM                     ItemInventories_wk
GROUP BY          InventoryDate, StoreId, CrDateTime, UpDateTime

/*
⑩作成済みのたな卸しデータを本番テーブルに投入する。
*/
INSERT INTO     ItemInventories
                                  (InventoryDate, ItemId, StoreId, SectionId, InventoryCount, UnitPrice, Price, UnitPriceTaxIn, PriceTaxIn, DelFlg, CrDateTime, UpDateTime, 
                                  LastUpdateUserId)
SELECT                   InventoryDate, ItemId, StoreId, SectionId, InventoryCount, UnitPrice, Price, UnitPriceTaxIn, PriceTaxIn, DelFlg, CrDateTime, UpDateTime, 
                                  LastUpdateUserId
FROM                     ItemInventories_wk
go

/*
⑪後工程
InventoryALL_XXX　（XXXは店舗コード）のテーブルは物理的にテーブル削除。

⑫翌朝の単品棚卸し更新はそれなりに時間がかかるので注意する。
*/ 

⑬テーブル最適化
DBCC SHOWCONTIG ('ItemInventories')
DBCC DBREINDEX ('ItemInventories')

DBCC SHOWCONTIG ('ItemInventoriesQuant')
DBCC DBREINDEX ('ItemInventoriesQuant')


⑭
シノプス 在庫連携

　（１）夜に、店舗登録の在庫修正登録データを出力する。
　　　D:\Batch\YsskSinopsBatch\ZaikoShusei\YsskSinopsBatch.ExportZaikoShusei.exe

　（2）D:\BmNet\Sinops\BatchManagerNet.config で在庫修正の出力を外す。
　　　※（夜間処理後）に、翌日も同じ棚卸更新しなければ戻す。
　
　（3）C:\Users\<USER>\Desktop\作業\棚卸関連\シノプスzaikoshusei摘出SQL.txt
      の内容を見て、今回登録したエイジスの登録データをシノプス形式にする。

　（4）(1)で出力したデータに付け足す。

　（5）夜間バッチで出力による上書きがなくFTPで送信される。
