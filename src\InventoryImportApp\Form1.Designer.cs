﻿namespace InventoryImportApp;

partial class Form1
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        folderPathLabel = new Label();
        folderPathTextBox = new TextBox();
        browseFolderButton = new Button();
        refreshButton = new Button();
        fileListView = new ListView();
        selectAllCheckBox = new CheckBox();
        selectedCountLabel = new Label();
        registrationDateLabel = new Label();
        registrationDatePicker = new DateTimePicker();
        importButton = new Button();
        statusLabel = new Label();
        progressBar = new ProgressBar();
        dataConfirmationLabel = new Label();
        dataGridView = new DataGridView();
        productionRegisterButton = new Button();
        exportCsvButton = new Button();
        SuspendLayout();
        // 
        // folderPathLabel
        // 
        folderPathLabel.AutoSize = true;
        folderPathLabel.Font = new Font("Yu Gothic UI", 11F);
        folderPathLabel.Location = new Point(20, 20);
        folderPathLabel.Name = "folderPathLabel";
        folderPathLabel.Size = new Size(110, 25);
        folderPathLabel.TabIndex = 0;
        folderPathLabel.Text = "親フォルダ：";
        // 
        // folderPathTextBox
        // 
        folderPathTextBox.Font = new Font("Yu Gothic UI", 10F);
        folderPathTextBox.Location = new Point(20, 50);
        folderPathTextBox.Name = "folderPathTextBox";
        folderPathTextBox.ReadOnly = true;
        folderPathTextBox.Size = new Size(600, 30);
        folderPathTextBox.TabIndex = 1;
        // 
        // browseFolderButton
        // 
        browseFolderButton.Font = new Font("Yu Gothic UI", 10F);
        browseFolderButton.Location = new Point(640, 48);
        browseFolderButton.Name = "browseFolderButton";
        browseFolderButton.Size = new Size(90, 48);
        browseFolderButton.TabIndex = 2;
        browseFolderButton.Text = "参照";
        browseFolderButton.UseVisualStyleBackColor = true;
        // 
        // refreshButton
        // 
        refreshButton.Font = new Font("Yu Gothic UI", 10F);
        refreshButton.Location = new Point(750, 48);
        refreshButton.Name = "refreshButton";
        refreshButton.Size = new Size(90, 48);
        refreshButton.TabIndex = 3;
        refreshButton.Text = "更新";
        refreshButton.UseVisualStyleBackColor = true;
        // 
        // fileListView
        // 
        fileListView.CheckBoxes = true;
        fileListView.Font = new Font("Yu Gothic UI", 9F);
        fileListView.FullRowSelect = true;
        fileListView.GridLines = true;
        fileListView.Location = new Point(20, 150);
        fileListView.Name = "fileListView";
        fileListView.Size = new Size(1150, 250);
        fileListView.TabIndex = 4;
        fileListView.UseCompatibleStateImageBehavior = false;
        fileListView.View = View.Details;
        // 
        // selectAllCheckBox
        // 
        selectAllCheckBox.AutoSize = true;
        selectAllCheckBox.Font = new Font("Yu Gothic UI", 10F);
        selectAllCheckBox.Location = new Point(20, 120);
        selectAllCheckBox.Name = "selectAllCheckBox";
        selectAllCheckBox.Size = new Size(86, 27);
        selectAllCheckBox.TabIndex = 5;
        selectAllCheckBox.Text = "全選択";
        selectAllCheckBox.UseVisualStyleBackColor = true;
        // 
        // selectedCountLabel
        // 
        selectedCountLabel.AutoSize = true;
        selectedCountLabel.Font = new Font("Yu Gothic UI", 10F);
        selectedCountLabel.Location = new Point(150, 123);
        selectedCountLabel.Name = "selectedCountLabel";
        selectedCountLabel.Size = new Size(146, 23);
        selectedCountLabel.TabIndex = 6;
        selectedCountLabel.Text = "選択済み: 0 / 0 件";
        // 
        // registrationDateLabel
        // 
        registrationDateLabel.AutoSize = true;
        registrationDateLabel.Font = new Font("Yu Gothic UI", 11F);
        registrationDateLabel.Location = new Point(400, 420);
        registrationDateLabel.Name = "registrationDateLabel";
        registrationDateLabel.Size = new Size(74, 25);
        registrationDateLabel.TabIndex = 7;
        registrationDateLabel.Text = "登録日：";
        // 
        // registrationDatePicker
        // 
        registrationDatePicker.Font = new Font("Yu Gothic UI", 10F);
        registrationDatePicker.Format = DateTimePickerFormat.Short;
        registrationDatePicker.Location = new Point(480, 418);
        registrationDatePicker.MaxDate = DateTime.Today.AddDays(1);
        registrationDatePicker.MinDate = DateTime.Today.AddDays(-1);
        registrationDatePicker.Name = "registrationDatePicker";
        registrationDatePicker.Size = new Size(130, 30);
        registrationDatePicker.TabIndex = 8;
        registrationDatePicker.Value = DateTime.Today;
        // 
        // importButton
        // 
        importButton.BackColor = Color.FromArgb(0, 120, 215);
        importButton.FlatAppearance.BorderSize = 0;
        importButton.FlatStyle = FlatStyle.Flat;
        importButton.Font = new Font("Yu Gothic UI", 12F, FontStyle.Bold);
        importButton.ForeColor = Color.White;
        importButton.Location = new Point(630, 410);
        importButton.Name = "importButton";
        importButton.Size = new Size(230, 60);
        importButton.TabIndex = 9;
        importButton.Text = "インポート";
        importButton.UseVisualStyleBackColor = false;
        // 
        // statusLabel
        // 
        statusLabel.AutoSize = true;
        statusLabel.Font = new Font("Yu Gothic UI", 10F);
        statusLabel.Location = new Point(20, 490);
        statusLabel.Name = "statusLabel";
        statusLabel.Size = new Size(78, 23);
        statusLabel.TabIndex = 10;
        statusLabel.Text = "準備完了";
        // 
        // progressBar
        // 
        progressBar.Location = new Point(20, 520);
        progressBar.Name = "progressBar";
        progressBar.Size = new Size(1150, 25);
        progressBar.TabIndex = 11;
        // 
        // dataConfirmationLabel
        // 
        dataConfirmationLabel.AutoSize = true;
        dataConfirmationLabel.Font = new Font("Yu Gothic UI", 12F, FontStyle.Bold);
        dataConfirmationLabel.Location = new Point(20, 560);
        dataConfirmationLabel.Name = "dataConfirmationLabel";
        dataConfirmationLabel.Size = new Size(334, 28);
        dataConfirmationLabel.TabIndex = 12;
        dataConfirmationLabel.Text = "インポート結果確認（ItemInventories_wk）";
        dataConfirmationLabel.Visible = false;
        // 
        // dataGridView
        // 
        dataGridView.AllowUserToAddRows = false;
        dataGridView.AllowUserToDeleteRows = false;
        dataGridView.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
        dataGridView.Font = new Font("Yu Gothic UI", 9F);
        dataGridView.Location = new Point(20, 600);
        dataGridView.Name = "dataGridView";
        dataGridView.ReadOnly = true;
        dataGridView.RowHeadersWidth = 51;
        dataGridView.Size = new Size(1150, 250);
        dataGridView.TabIndex = 13;
        dataGridView.Visible = false;
        // 
        // productionRegisterButton
        // 
        productionRegisterButton.BackColor = Color.FromArgb(220, 20, 60);
        productionRegisterButton.FlatAppearance.BorderSize = 0;
        productionRegisterButton.FlatStyle = FlatStyle.Flat;
        productionRegisterButton.Font = new Font("Yu Gothic UI", 14F, FontStyle.Bold);
        productionRegisterButton.ForeColor = Color.White;
        productionRegisterButton.Location = new Point(240, 800);
        productionRegisterButton.Name = "productionRegisterButton";
        productionRegisterButton.Size = new Size(200, 70);
        productionRegisterButton.TabIndex = 14;
        productionRegisterButton.Text = "本番登録";
        productionRegisterButton.UseVisualStyleBackColor = false;
        productionRegisterButton.Visible = false;
        //
        // exportCsvButton
        //
        exportCsvButton.BackColor = Color.FromArgb(70, 130, 180);
        exportCsvButton.FlatAppearance.BorderSize = 0;
        exportCsvButton.FlatStyle = FlatStyle.Flat;
        exportCsvButton.Font = new Font("Yu Gothic UI", 12F, FontStyle.Bold);
        exportCsvButton.ForeColor = Color.White;
        exportCsvButton.Location = new Point(20, 800);
        exportCsvButton.Name = "exportCsvButton";
        exportCsvButton.Size = new Size(200, 70);
        exportCsvButton.TabIndex = 15;
        exportCsvButton.Text = "CSVエクスポート";
        exportCsvButton.UseVisualStyleBackColor = false;
        exportCsvButton.Visible = false;
        exportCsvButton.Click += ExportCsvButton_Click;
        //
        // Form1
        // 
        AutoScaleDimensions = new SizeF(8F, 20F);
        AutoScaleMode = AutoScaleMode.Font;
        BackColor = Color.White;
        ClientSize = new Size(1200, 950);
        Controls.Add(exportCsvButton);
        Controls.Add(productionRegisterButton);
        Controls.Add(dataGridView);
        Controls.Add(dataConfirmationLabel);
        Controls.Add(progressBar);
        Controls.Add(statusLabel);
        Controls.Add(importButton);
        Controls.Add(registrationDatePicker);
        Controls.Add(registrationDateLabel);
        Controls.Add(selectedCountLabel);
        Controls.Add(selectAllCheckBox);
        Controls.Add(fileListView);
        Controls.Add(refreshButton);
        Controls.Add(browseFolderButton);
        Controls.Add(folderPathTextBox);
        Controls.Add(folderPathLabel);
        FormBorderStyle = FormBorderStyle.Sizable;
        MaximizeBox = true;
        MinimumSize = new Size(1200, 950);
        Name = "Form1";
        StartPosition = FormStartPosition.CenterScreen;
        Text = "棚卸インポートシステム";
        ResumeLayout(false);
        PerformLayout();
    }

    #endregion

    private Label folderPathLabel;
    private TextBox folderPathTextBox;
    private Button browseFolderButton;
    private Button refreshButton;
    private ListView fileListView;
    private CheckBox selectAllCheckBox;
    private Label selectedCountLabel;
    private Label registrationDateLabel;
    private DateTimePicker registrationDatePicker;
    private Button importButton;
    private Label statusLabel;
    private ProgressBar progressBar;
    private Label dataConfirmationLabel;
    private DataGridView dataGridView;
    private Button productionRegisterButton;
    private Button exportCsvButton;
}
