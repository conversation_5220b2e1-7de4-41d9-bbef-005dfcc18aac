namespace InventoryImportApp.Core.Models;

/// <summary>
/// インポート処理の進捗情報を表すクラス
/// </summary>
public class ImportProgress
{
    /// <summary>
    /// 現在の処理フェーズ（例: "CSVファイル読込中...", "マスタ結合処理中..."）
    /// </summary>
    public string Phase { get; set; } = string.Empty;
    
    /// <summary>
    /// 進捗率（0-100）
    /// </summary>
    public int ProgressPercentage { get; set; }
    
    /// <summary>
    /// 現在処理中のファイル名（CSVインポート時）
    /// </summary>
    public string CurrentFile { get; set; } = string.Empty;
    
    /// <summary>
    /// 処理済みレコード数
    /// </summary>
    public int ProcessedRecords { get; set; }
    
    /// <summary>
    /// 総レコード数（分かっている場合）
    /// </summary>
    public int TotalRecords { get; set; }
    
    /// <summary>
    /// 詳細メッセージ（エラー情報等）
    /// </summary>
    public string DetailMessage { get; set; } = string.Empty;
}
