namespace InventoryImportApp.Core.Models;

public class InventoryFileInfo
{
    public string StoreCode { get; set; } = string.Empty;
    public string FileName { get; set; } = string.Empty;
    public string FilePath { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public DateTime LastModified { get; set; }
    public bool IsSelected { get; set; }
    public int RecordCount { get; set; }
    
    public string FormattedFileSize => FormatFileSize(FileSize);
    public string FormattedLastModified => LastModified.ToString("yyyy/MM/dd HH:mm:ss");
    
    private static string FormatFileSize(long bytes)
    {
        if (bytes < 1024) return $"{bytes} B";
        if (bytes < 1024 * 1024) return $"{bytes / 1024:F1} KB";
        if (bytes < 1024 * 1024 * 1024) return $"{bytes / (1024 * 1024):F1} MB";
        return $"{bytes / (1024 * 1024 * 1024):F1} GB";
    }
} 