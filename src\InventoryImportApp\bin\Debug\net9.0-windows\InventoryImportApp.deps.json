{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"InventoryImportApp/1.0.0": {"dependencies": {"InventoryImportApp.Core": "1.0.0", "InventoryImportApp.Data": "1.0.0", "Microsoft.Data.SqlClient": "6.1.0", "NLog": "6.0.2"}, "runtime": {"InventoryImportApp.dll": {}}}, "Azure.Core/1.44.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.ClientModel": "1.1.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "6.0.0", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "9.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.44.1.0", "fileVersion": "1.4400.124.50905"}}}, "Azure.Identity/1.13.2": {"dependencies": {"Azure.Core": "1.44.1", "Microsoft.Identity.Client": "4.67.2", "Microsoft.Identity.Client.Extensions.Msal": "4.67.2", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net8.0/Azure.Identity.dll": {"assemblyVersion": "1.13.2.0", "fileVersion": "1.1300.225.6404"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Bcl.Cryptography/9.0.4": {"runtime": {"lib/net9.0/Microsoft.Bcl.Cryptography.dll": {"assemblyVersion": "9.0.0.4", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Data.SqlClient/6.1.0": {"dependencies": {"Azure.Identity": "1.13.2", "Microsoft.Bcl.Cryptography": "9.0.4", "Microsoft.Data.SqlClient.SNI.runtime": "6.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.4", "Microsoft.IdentityModel.JsonWebTokens": "7.7.1", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.7.1", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "9.0.4", "System.Security.Cryptography.Pkcs": "9.0.4", "System.Text.Json": "9.0.5"}, "runtime": {"lib/net9.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.10.25206.1"}}, "resources": {"lib/net9.0/cs/Microsoft.Data.SqlClient.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.Data.SqlClient.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.Data.SqlClient.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net9.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.10.25206.1"}, "runtimes/win/lib/net9.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.0", "fileVersion": "6.10.25206.1"}}}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"runtimeTargets": {"runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "6.2.0.0"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.2.0.0"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "6.2.0.0"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Caching.Memory/9.0.4": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Options/9.0.4": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Primitives/9.0.4": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Identity.Client/4.67.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.7.1", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.67.2.0", "fileVersion": "4.67.2.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.67.2": {"dependencies": {"Microsoft.Identity.Client": "4.67.2", "System.Security.Cryptography.ProtectedData": "9.0.4"}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.67.2.0", "fileVersion": "4.67.2.0"}}}, "Microsoft.IdentityModel.Abstractions/7.7.1": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "7.7.1.0", "fileVersion": "7.7.1.50719"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.7.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.7.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "7.7.1.0", "fileVersion": "7.7.1.50719"}}}, "Microsoft.IdentityModel.Logging/7.7.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.7.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "7.7.1.0", "fileVersion": "7.7.1.50719"}}}, "Microsoft.IdentityModel.Protocols/7.7.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.7.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "7.7.1.0", "fileVersion": "7.7.1.50719"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.7.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.7.1", "System.IdentityModel.Tokens.Jwt": "7.7.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "7.7.1.0", "fileVersion": "7.7.1.50719"}}}, "Microsoft.IdentityModel.Tokens/7.7.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.7.1"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "7.7.1.0", "fileVersion": "7.7.1.50719"}}}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NLog/6.0.2": {"runtime": {"lib/netstandard2.1/NLog.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.2.4328"}}}, "System.ClientModel/1.1.0": {"dependencies": {"System.Memory.Data": "6.0.0", "System.Text.Json": "9.0.5"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.100.24.46703"}}}, "System.Configuration.ConfigurationManager/9.0.4": {"dependencies": {"System.Diagnostics.EventLog": "9.0.4", "System.Security.Cryptography.ProtectedData": "9.0.4"}, "runtime": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.EventLog/9.0.4": {"runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}}, "System.IdentityModel.Tokens.Jwt/7.7.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.7.1", "Microsoft.IdentityModel.Tokens": "7.7.1"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "7.7.1.0", "fileVersion": "7.7.1.50719"}}}, "System.Memory/4.5.5": {}, "System.Memory.Data/6.0.0": {"dependencies": {"System.Text.Json": "9.0.5"}, "runtime": {"lib/net6.0/System.Memory.Data.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.21.52210"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.Cryptography.Pkcs/9.0.4": {"runtime": {"lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}}, "System.Security.Cryptography.ProtectedData/9.0.4": {"runtime": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.425.16305"}}}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/9.0.5": {"runtime": {"lib/net9.0/System.Text.Json.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.525.21509"}}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "InventoryImportApp.Core/1.0.0": {"dependencies": {"Microsoft.Data.SqlClient": "6.1.0", "NLog": "6.0.2"}, "runtime": {"InventoryImportApp.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "InventoryImportApp.Data/1.0.0": {"dependencies": {"InventoryImportApp.Core": "1.0.0"}, "runtime": {"InventoryImportApp.Data.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"InventoryImportApp/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.Core/1.44.1": {"type": "package", "serviceable": true, "sha512": "sha512-YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "path": "azure.core/1.44.1", "hashPath": "azure.core.1.44.1.nupkg.sha512"}, "Azure.Identity/1.13.2": {"type": "package", "serviceable": true, "sha512": "sha512-CngQVQELdzFmsGSWyGIPIUOCrII7nApMVWxVmJCKQQrWxRXcNquCsZ+njRJRnhFUfD+KMAhpjyRCaceE4EOL6A==", "path": "azure.identity/1.13.2", "hashPath": "azure.identity.1.13.2.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.Bcl.Cryptography/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-YgZYAWzyNuPVtPq6WNm0bqOWNjYaWgl5mBWTGZyNoXitYBUYSp6iUB9AwK0V1mo793qRJUXz2t6UZrWITZSvuQ==", "path": "microsoft.bcl.cryptography/9.0.4", "hashPath": "microsoft.bcl.cryptography.9.0.4.nupkg.sha512"}, "Microsoft.Data.SqlClient/6.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-xLUg2ZKmtYiZc2NhOhkXzL6jm28RU1eNGjms5QX5GecRjmNnqdt7F2zWjlbeSqngkODrvdFiQdbF7nHG8x3/5A==", "path": "microsoft.data.sqlclient/6.1.0", "hashPath": "microsoft.data.sqlclient.6.1.0.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-f+pRODTWX7Y67jXO3T5S2dIPZ9qMJNySjlZT/TKmWVNWe19N8jcWmHaqHnnchaq3gxEKv1SWVY5EFzOD06l41w==", "path": "microsoft.data.sqlclient.sni.runtime/6.0.2", "hashPath": "microsoft.data.sqlclient.sni.runtime.6.0.2.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-imcZ5BGhBw5mNsWLepBbqqumWaFe0GtvyCvne2/2wsDIBRa2+Lhx4cU/pKt/4BwOizzUEOls2k1eOJQXHGMalg==", "path": "microsoft.extensions.caching.abstractions/9.0.4", "hashPath": "microsoft.extensions.caching.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-G5rEq1Qez5VJDTEyRsRUnewAspKjaY57VGsdZ8g8Ja6sXXzoiI3PpTd1t43HjHqNWD5A06MQveb2lscn+2CU+w==", "path": "microsoft.extensions.caching.memory/9.0.4", "hashPath": "microsoft.extensions.caching.memory.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-UI0TQPVkS78bFdjkTodmkH0Fe8lXv9LnhGFKgKrsgUJ5a5FVdFRcgjIkBVLbGgdRhxWirxH/8IXUtEyYJx6GQg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.4", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-0MXlimU4Dud6t+iNi5NEz3dO2w1HXdhoOLaYFuLPCjAsvlPQGwOT6V2KZRMLEhCAm/stSZt1AUv0XmDdkjvtbw==", "path": "microsoft.extensions.logging.abstractions/9.0.4", "hashPath": "microsoft.extensions.logging.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-fiFI2+58kicqVZyt/6obqoFwHiab7LC4FkQ3mmiBJ28Yy4fAvy2+v9MRnSvvlOO8chTOjKsdafFl/K9veCPo5g==", "path": "microsoft.extensions.options/9.0.4", "hashPath": "microsoft.extensions.options.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-SPFyMjyku1nqTFFJ928JAMd0QnRe4xjE7KeKnZMWXf3xk+6e0WiOZAluYtLdbJUXtsl2cCRSi8cBquJ408k8RA==", "path": "microsoft.extensions.primitives/9.0.4", "hashPath": "microsoft.extensions.primitives.9.0.4.nupkg.sha512"}, "Microsoft.Identity.Client/4.67.2": {"type": "package", "serviceable": true, "sha512": "sha512-37t0TfekfG6XM8kue/xNaA66Qjtti5Qe1xA41CK+bEd8VD76/oXJc+meFJHGzygIC485dCpKoamG/pDfb9Qd7Q==", "path": "microsoft.identity.client/4.67.2", "hashPath": "microsoft.identity.client.4.67.2.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.67.2": {"type": "package", "serviceable": true, "sha512": "sha512-DKs+Lva6csEUZabw+JkkjtFgVmcXh4pJeQy5KH5XzPOaKNoZhAMYj1qpKd97qYTZKXIFH12bHPk0DA+6krw+Cw==", "path": "microsoft.identity.client.extensions.msal/4.67.2", "hashPath": "microsoft.identity.client.extensions.msal.4.67.2.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-S7sHg6gLg7oFqNGLwN1qSbJDI+QcRRj8SuJ1jHyCmKSipnF6ZQL+tFV2NzVfGj/xmGT9TykQdQiBN+p5Idl4TA==", "path": "microsoft.identitymodel.abstractions/7.7.1", "hashPath": "microsoft.identitymodel.abstractions.7.7.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-3Izi75UCUssvo8LPx3OVnEeZay58qaFicrtSnbtUt7q8qQi0gy46gh4V8VUTkMVMKXV6VMyjBVmeNNgeCUJuIw==", "path": "microsoft.identitymodel.jsonwebtokens/7.7.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.7.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-BZNgSq/o8gsKExdYoBKPR65fdsxW0cTF8PsdqB8y011AGUJJW300S/ZIsEUD0+sOmGc003Gwv3FYbjrVjvsLNQ==", "path": "microsoft.identitymodel.logging/7.7.1", "hashPath": "microsoft.identitymodel.logging.7.7.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-h+fHHBGokepmCX+QZXJk4Ij8OApCb2n2ktoDkNX5CXteXsOxTHMNgjPGpAwdJMFvAL7TtGarUnk3o97NmBq2QQ==", "path": "microsoft.identitymodel.protocols/7.7.1", "hashPath": "microsoft.identitymodel.protocols.7.7.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-yT2Hdj8LpPbcT9C9KlLVxXl09C8zjFaVSaApdOwuecMuoV4s6Sof/mnTDz/+F/lILPIBvrWugR9CC7iRVZgbfQ==", "path": "microsoft.identitymodel.protocols.openidconnect/7.7.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.7.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-fQ0VVCba75lknUHGldi3iTKAYUQqbzp1Un8+d9cm9nON0Gs8NAkXddNg8iaUB0qi/ybtAmNWizTR4avdkCJ9pQ==", "path": "microsoft.identitymodel.tokens/7.7.1", "hashPath": "microsoft.identitymodel.tokens.7.7.1.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "NLog/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-kfjfjcjh/hHXLJ0TbEUH6ajb2jQFmwk/23nyYW9iPZ6cj5769SyeDTbUwGI7LSVUk5iTRJoC6CTKKmWrXK79oA==", "path": "nlog/6.0.2", "hashPath": "nlog.6.0.2.nupkg.sha512"}, "System.ClientModel/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UocOlCkxLZrG2CKMAAImPcldJTxeesHnHGHwhJ0pNlZEvEXcWKuQvVOER2/NiOkJGRJk978SNdw3j6/7O9H1lg==", "path": "system.clientmodel/1.1.0", "hashPath": "system.clientmodel.1.1.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-dvjqKp+2LpGid6phzrdrS/2mmEPxFl3jE1+L7614q4ZChKbLJCpHXg6sBILlCCED1t//EE+un/UdAetzIMpqnw==", "path": "system.configuration.configurationmanager/9.0.4", "hashPath": "system.configuration.configurationmanager.9.0.4.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-getRQEXD8idlpb1KW56XuxImMy0FKp2WJPDf3Qr0kI/QKxxJSftqfDFVo0DZ3HCJRLU73qHSruv5q2l5O47jQQ==", "path": "system.diagnostics.eventlog/9.0.4", "hashPath": "system.diagnostics.eventlog.9.0.4.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-rQkO1YbAjLwnDJSMpRhRtrc6XwIcEOcUvoEcge+evurpzSZM3UNK+MZfD3sKyTlYsvknZ6eJjSBfnmXqwOsT9Q==", "path": "system.identitymodel.tokens.jwt/7.7.1", "hashPath": "system.identitymodel.tokens.jwt.7.7.1.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ntFHArH3I4Lpjf5m4DCXQHJuGwWPNVJPaAvM95Jy/u+2Yzt2ryiyIN04LAogkjP9DeRcEOiviAjQotfmPq/FrQ==", "path": "system.memory.data/6.0.0", "hashPath": "system.memory.data.6.0.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-cUFTcMlz/Qw9s90b2wnWSCvHdjv51Bau9FQqhsr4TlwSe1OX+7SoXUqphis5G74MLOvMOCghxPPlEqOdCrVVGA==", "path": "system.security.cryptography.pkcs/9.0.4", "hashPath": "system.security.cryptography.pkcs.9.0.4.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-o94k2RKuAce3GeDMlUvIXlhVa1kWpJw95E6C9LwW0KlG0nj5+SgCiIxJ2Eroqb9sLtG1mEMbFttZIBZ13EJPvQ==", "path": "system.security.cryptography.protecteddata/9.0.4", "hashPath": "system.security.cryptography.protecteddata.9.0.4.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/9.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-rnP61ZfloTgPQPe7ecr36loNiGX3g1PocxlKHdY/FUpDSsExKkTxpMAlB4X35wNEPr1X7mkYZuQvW3Lhxmu7KA==", "path": "system.text.json/9.0.5", "hashPath": "system.text.json.9.0.5.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "InventoryImportApp.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "InventoryImportApp.Data/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}