{"_comment": "ステージング環境・リモート接続用設定", "InventoryImportApp": {"DatabaseConnectionString": "Data Source=192.7.49.31;Initial Catalog=YsskKikan;User ID=sa;Password=**********;TrustServerCertificate=True;Connection Timeout=30;", "DefaultParentFolder": "C:\\YSInventoryUpdate\\out", "UI": {"WindowTitle": "棚卸結果取込システム (Staging)", "ConfirmationRequired": true}}, "Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Information"}, "File": {"LogFilePath": "logs/staging-inventory-import-{Date}.log"}}}