SELECT                      TOP (100) PERCENT dbo.Stores.Id AS StoreId, dbo.Items.Id AS ItemId, 
                                      dbo.V_InvMkZeroItems.InvCount AS InventoryCount, Sections_2.Id AS SectionId, 12 AS LastUpdateUser, 0 AS DelFlg, 
                                      dbo.V_InvMkZeroItems.InvDate, CONVERT(DATETIME, '2025-02-28 23:59:59', 120) AS CrDate, CONVERT(DATETIME, 
                                      '2025-02-28 23:59:59', 120) AS UpdDate
FROM                         dbo.V_InvMkZeroItems INNER JOIN
                                      dbo.Stores ON dbo.V_InvMkZeroItems.StoreCode = dbo.Stores.StoreCode INNER JOIN
                                      dbo.Sections INNER JOIN
                                      dbo.Items ON dbo.Sections.Id = dbo.Items.SectionId INNER JOIN
                                      dbo.Sections AS Sections_1 ON dbo.Sections.ParentCodeId = Sections_1.Id INNER JOIN
                                      dbo.Sections AS Sections_2 ON Sections_1.ParentCodeId = Sections_2.Id ON 
                                      dbo.V_InvMkZeroItems.ItemCode = dbo.Items.ItemCode
WHERE                       (dbo.Items.DelFlg = 0)
ORDER BY               StoreId, ItemId