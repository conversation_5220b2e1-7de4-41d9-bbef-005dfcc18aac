2025-07-30 18:03:09.3425 [INFO] InventoryImportApp.Form1 - === アプリケーション開始 === 
2025-07-30 18:03:09.3512 [INFO] InventoryImportApp.Form1 - 実行ファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\ 
2025-07-30 18:03:09.3512 [INFO] InventoryImportApp.Form1 - ログファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\logs 
2025-07-30 18:03:14.7999 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理開始 === 
2025-07-30 18:03:14.7999 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 選択ファイル数: 1件 
2025-07-30 18:03:14.7999 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 登録日時: 2025-07-30 23:59:59 
2025-07-30 18:03:14.7999 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 対象ファイル: InventoryAll_365.csv (10,330件) 
2025-07-30 18:03:14.7999 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化開始 
2025-07-30 18:03:31.3430 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化完了 
2025-07-30 18:03:31.3430 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート開始 
2025-07-30 18:09:12.1159 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート完了 - 10,330件 
2025-07-30 18:09:12.1162 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理開始 
2025-07-30 18:09:12.1723 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - InventoryAll_wkレコード数: 10,330件 
2025-07-30 18:09:12.2648 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ店舗数: 121件 
2025-07-30 18:09:12.2648 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ商品数: 392,432件 
2025-07-30 18:09:12.4194 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合可能レコード数: 10,330件 
2025-07-30 18:09:12.6695 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合SQL実行結果: 10,330件処理 
2025-07-30 18:09:12.7513 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合後統計 - 在庫合計: 104,338, 平均在庫: 10.1 
2025-07-30 18:09:12.7513 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理完了 
2025-07-30 18:09:26.9961 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理完了 === 
2025-07-30 18:09:26.9961 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理時間: 372.2秒 
2025-07-30 18:09:26.9961 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - CSVレコード数: 10,330件 
2025-07-30 18:09:26.9961 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理レコード数: 5,845件 
2025-07-30 18:09:26.9961 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 在庫0補完数: 6,031件 
2025-07-30 18:09:26.9961 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 重複削除数: 10,516件 
2025-07-30 18:14:48.4708 [INFO] InventoryImportApp.Form1 - === アプリケーション開始 === 
2025-07-30 18:14:48.4829 [INFO] InventoryImportApp.Form1 - 実行ファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\ 
2025-07-30 18:14:48.4829 [INFO] InventoryImportApp.Form1 - ログファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\logs 
2025-07-30 18:14:57.6198 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理開始 === 
2025-07-30 18:14:57.6198 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 選択ファイル数: 1件 
2025-07-30 18:14:57.6198 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 登録日時: 2025-07-30 23:59:59 
2025-07-30 18:14:57.6198 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 対象ファイル: InventoryAll_367.csv (9,535件) 
2025-07-30 18:14:57.6198 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化開始 
2025-07-30 18:15:14.2265 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化完了 
2025-07-30 18:15:14.2265 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート開始 
2025-07-30 18:19:11.0540 [INFO] InventoryImportApp.Form1 - === アプリケーション開始 === 
2025-07-30 18:19:11.0540 [INFO] InventoryImportApp.Form1 - 実行ファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\ 
2025-07-30 18:19:11.0540 [INFO] InventoryImportApp.Form1 - ログファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\logs 
2025-07-30 18:19:15.6279 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理開始 === 
2025-07-30 18:19:15.6279 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 選択ファイル数: 1件 
2025-07-30 18:19:15.6279 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 登録日時: 2025-07-30 23:59:59 
2025-07-30 18:19:15.6279 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 対象ファイル: InventoryAll_360.csv (9,266件) 
2025-07-30 18:19:15.6279 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化開始 
2025-07-30 18:19:32.2471 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化完了 
2025-07-30 18:19:32.2471 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート開始 
2025-07-30 18:19:32.2613 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - CSVファイル読み込み開始: InventoryAll_360.csv (9,266行) 
2025-07-30 18:19:32.3007 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - DataTable作成完了: 9,266行 
2025-07-30 18:19:32.7544 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - BulkCopy完了: 9,266行 
2025-07-30 18:19:32.7544 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート完了 - 9,266件 
2025-07-30 18:19:32.7544 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理開始 
2025-07-30 18:19:32.8100 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - InventoryAll_wkレコード数: 9,266件 
2025-07-30 18:19:32.8980 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ店舗数: 121件 
2025-07-30 18:19:32.8980 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ商品数: 392,432件 
2025-07-30 18:19:33.0634 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合可能レコード数: 9,266件 
2025-07-30 18:19:33.2811 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合SQL実行結果: 9,266件処理 
2025-07-30 18:19:33.3618 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合後統計 - 在庫合計: 110,673, 平均在庫: 11.9 
2025-07-30 18:19:33.3618 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理完了 
2025-07-30 18:19:48.0810 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理完了 === 
2025-07-30 18:19:48.0810 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理時間: 32.5秒 
2025-07-30 18:19:48.0810 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - CSVレコード数: 9,266件 
2025-07-30 18:19:48.0810 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理レコード数: 141件 
2025-07-30 18:19:48.0810 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 在庫0補完数: 253件 
2025-07-30 18:19:48.0820 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 重複削除数: 9,378件 
2025-07-30 18:19:56.6250 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - データ確認用のワークデータ取得開始 
2025-07-30 18:19:56.6898 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - データ確認用のワークデータ取得完了: 141件 
2025-07-30 18:26:42.3234 [INFO] InventoryImportApp.Form1 - === アプリケーション開始 === 
2025-07-30 18:26:42.3496 [INFO] InventoryImportApp.Form1 - 実行ファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\ 
2025-07-30 18:26:42.3496 [INFO] InventoryImportApp.Form1 - ログファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\logs 
2025-07-30 18:26:55.5895 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理開始 === 
2025-07-30 18:26:55.5895 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 選択ファイル数: 1件 
2025-07-30 18:26:55.5895 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 登録日時: 2025-07-30 23:59:59 
2025-07-30 18:26:55.5895 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 対象ファイル: InventoryAll_363.csv (9,346件) 
2025-07-30 18:26:55.5895 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化開始 
2025-07-30 18:27:13.2367 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化完了 
2025-07-30 18:27:13.2367 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート開始 
2025-07-30 18:27:13.2530 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - CSVファイル読み込み開始: InventoryAll_363.csv (9,346行) 
2025-07-30 18:27:13.2709 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - DataTable作成完了: 9,346行 
2025-07-30 18:27:13.6977 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - BulkCopy完了: 9,346行 
2025-07-30 18:27:13.6977 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート完了 - 9,346件 
2025-07-30 18:27:13.6977 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理開始 
2025-07-30 18:27:13.7576 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - InventoryAll_wkレコード数: 9,346件 
2025-07-30 18:27:13.9643 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ店舗数: 121件 
2025-07-30 18:27:13.9643 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ商品数: 392,432件 
2025-07-30 18:27:14.2823 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合可能レコード数: 9,346件 
2025-07-30 18:27:14.5716 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合SQL実行結果: 9,346件処理 
2025-07-30 18:27:14.6767 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合後統計 - 在庫合計: 87,461, 平均在庫: 9.4 
2025-07-30 18:27:14.6767 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理完了 
2025-07-30 18:27:33.8371 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理完了 === 
2025-07-30 18:27:33.8371 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理時間: 38.2秒 
2025-07-30 18:27:33.8371 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - CSVレコード数: 9,346件 
2025-07-30 18:27:33.8371 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理レコード数: 5,747件 
2025-07-30 18:27:33.8371 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 在庫0補完数: 5,890件 
2025-07-30 18:27:33.8371 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 重複削除数: 9,489件 
2025-07-30 18:27:42.3820 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - データ確認用のワークデータ取得開始 
2025-07-30 18:27:42.6118 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - データ確認用のワークデータ取得完了: 1000件 
2025-07-30 18:33:51.6283 [INFO] InventoryImportApp.Form1 - === アプリケーション開始 === 
2025-07-30 18:33:51.6283 [INFO] InventoryImportApp.Form1 - 実行ファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\ 
2025-07-30 18:33:51.6283 [INFO] InventoryImportApp.Form1 - ログファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\logs 
2025-07-30 18:33:56.8857 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理開始 === 
2025-07-30 18:33:56.8857 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 選択ファイル数: 1件 
2025-07-30 18:33:56.8857 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 登録日時: 2025-07-30 23:59:59 
2025-07-30 18:33:56.8857 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 対象ファイル: InventoryAll_364.csv (10,964件) 
2025-07-30 18:33:56.8857 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化開始 
2025-07-30 18:34:13.5016 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化完了 
2025-07-30 18:34:13.5016 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート開始 
2025-07-30 18:34:13.5181 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - CSVファイル読み込み開始: InventoryAll_364.csv (10,964行) 
2025-07-30 18:34:13.5584 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - DataTable作成完了: 10,964行 
2025-07-30 18:34:14.0632 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - BulkCopy完了: 10,964行 
2025-07-30 18:34:14.0632 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート完了 - 10,964件 
2025-07-30 18:34:14.0632 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理開始 
2025-07-30 18:34:14.1132 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - InventoryAll_wkレコード数: 10,964件 
2025-07-30 18:34:14.2442 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ店舗数: 121件 
2025-07-30 18:34:14.2442 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ商品数: 392,432件 
2025-07-30 18:34:14.4271 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合可能レコード数: 10,964件 
2025-07-30 18:34:14.7433 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合SQL実行結果: 10,964件処理 
2025-07-30 18:34:14.8180 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合後統計 - 在庫合計: 122,875, 平均在庫: 11.2 
2025-07-30 18:34:14.8180 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理完了 
2025-07-30 18:34:28.3575 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理完了 === 
2025-07-30 18:34:28.3575 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理時間: 31.5秒 
2025-07-30 18:34:28.3575 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - CSVレコード数: 10,964件 
2025-07-30 18:34:28.3575 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理レコード数: 6,208件 
2025-07-30 18:34:28.3575 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 在庫0補完数: 6,387件 
2025-07-30 18:34:28.3575 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 重複削除数: 11,143件 
2025-07-30 18:42:16.8954 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - データ確認用のワークデータ取得開始 
2025-07-30 18:42:33.7812 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - データ確認用のワークデータ取得完了: 6,208件（全件表示） 
2025-07-30 18:53:37.3542 [INFO] InventoryImportApp.Form1 - === アプリケーション開始 === 
2025-07-30 18:53:37.3706 [INFO] InventoryImportApp.Form1 - 実行ファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\ 
2025-07-30 18:53:37.3706 [INFO] InventoryImportApp.Form1 - ログファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\logs 
2025-07-30 18:53:42.6051 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理開始 === 
2025-07-30 18:53:42.6051 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 選択ファイル数: 1件 
2025-07-30 18:53:42.6051 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 登録日時: 2025-07-30 23:59:59 
2025-07-30 18:53:42.6051 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 対象ファイル: InventoryAll_360.csv (9,266件) 
2025-07-30 18:53:42.6051 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化開始 
2025-07-30 18:53:59.2517 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化完了 
2025-07-30 18:53:59.2517 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート開始 
2025-07-30 18:53:59.2728 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - CSVファイル読み込み開始: InventoryAll_360.csv (9,266行) 
2025-07-30 18:53:59.3020 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - DataTable作成完了: 9,266行 
2025-07-30 18:53:59.6957 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - BulkCopy完了: 9,266行 
2025-07-30 18:53:59.6957 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート完了 - 9,266件 
2025-07-30 18:53:59.6957 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理開始 
2025-07-30 18:53:59.7494 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - InventoryAll_wkレコード数: 9,266件 
2025-07-30 18:53:59.8934 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ店舗数: 121件 
2025-07-30 18:53:59.8934 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ商品数: 392,432件 
2025-07-30 18:54:00.0703 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合可能レコード数: 9,266件 
2025-07-30 18:54:00.3262 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合SQL実行結果: 9,266件処理 
2025-07-30 18:54:00.4073 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合後統計 - 在庫合計: 110,673, 平均在庫: 11.9 
2025-07-30 18:54:00.4073 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理完了 
2025-07-30 18:54:23.1765 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理完了 === 
2025-07-30 18:54:23.1765 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理時間: 40.6秒 
2025-07-30 18:54:23.1765 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - CSVレコード数: 9,266件 
2025-07-30 18:54:23.1765 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理レコード数: 141件 
2025-07-30 18:54:23.1765 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 在庫0補完数: 253件 
2025-07-30 18:54:23.1765 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 重複削除数: 9,378件 
2025-07-30 18:54:24.5811 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - データ確認用のワークデータ取得開始 
2025-07-30 18:54:24.6551 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - データ確認用のワークデータ取得完了: 141件（全件表示） 
2025-07-30 18:54:37.4478 [INFO] InventoryImportApp.Form1 - CSVエクスポート完了: C:\Users\<USER>\Desktop\ItemInventories_wk_20250730_185430.csv (141行) 
2025-07-30 19:01:15.8285 [INFO] InventoryImportApp.Form1 - === アプリケーション開始 === 
2025-07-30 19:01:15.8365 [INFO] InventoryImportApp.Form1 - 実行ファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\ 
2025-07-30 19:01:15.8365 [INFO] InventoryImportApp.Form1 - ログファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\logs 
2025-07-30 19:01:24.1121 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理開始 === 
2025-07-30 19:01:24.1121 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 選択ファイル数: 1件 
2025-07-30 19:01:24.1121 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 登録日時: 2025-07-30 23:59:59 
2025-07-30 19:01:24.1144 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 対象ファイル: InventoryAll_363.csv (9,346件) 
2025-07-30 19:01:24.1144 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化開始 
2025-07-30 19:01:40.8070 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化完了 
2025-07-30 19:01:40.8070 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート開始 
2025-07-30 19:01:40.8276 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - CSVファイル読み込み開始: InventoryAll_363.csv (9,346行) 
2025-07-30 19:01:40.8497 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - DataTable作成完了: 9,346行 
2025-07-30 19:01:41.2901 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - BulkCopy完了: 9,346行 
2025-07-30 19:01:41.2901 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート完了 - 9,346件 
2025-07-30 19:01:41.2901 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理開始 
2025-07-30 19:01:41.3529 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - InventoryAll_wkレコード数: 9,346件 
2025-07-30 19:01:41.4455 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ店舗数: 121件 
2025-07-30 19:01:41.4455 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ商品数: 392,432件 
2025-07-30 19:01:41.6111 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合可能レコード数: 9,346件 
2025-07-30 19:01:41.8409 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合SQL実行結果: 9,346件処理 
2025-07-30 19:01:41.9123 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合後統計 - 在庫合計: 87,461, 平均在庫: 9.4 
2025-07-30 19:01:41.9123 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理完了 
2025-07-30 19:01:47.4428 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理完了 === 
2025-07-30 19:01:47.4428 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理時間: 23.3秒 
2025-07-30 19:01:47.4428 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - CSVレコード数: 9,346件 
2025-07-30 19:01:47.4428 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理レコード数: 5,747件 
2025-07-30 19:01:47.4428 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 在庫0補完数: 5,890件 
2025-07-30 19:01:47.4428 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 重複削除数: 9,489件 
2025-07-30 19:01:48.9740 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - データ確認用のワークデータ取得開始 
2025-07-30 19:01:49.6827 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - データ確認用のワークデータ取得完了: 5,747件（全件表示） 
2025-07-30 19:04:41.1041 [INFO] InventoryImportApp.Form1 - === アプリケーション開始 === 
2025-07-30 19:04:41.1231 [INFO] InventoryImportApp.Form1 - 実行ファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\ 
2025-07-30 19:04:41.1231 [INFO] InventoryImportApp.Form1 - ログファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\logs 
2025-07-30 19:04:48.8569 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理開始 === 
2025-07-30 19:04:48.8569 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 選択ファイル数: 1件 
2025-07-30 19:04:48.8569 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 登録日時: 2025-07-30 23:59:59 
2025-07-30 19:04:48.8569 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 対象ファイル: InventoryAll_362.csv (9,986件) 
2025-07-30 19:04:48.8569 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化開始 
2025-07-30 19:05:05.4304 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化完了 
2025-07-30 19:05:05.4305 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート開始 
2025-07-30 19:05:05.4444 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - CSVファイル読み込み開始: InventoryAll_362.csv (9,986行) 
2025-07-30 19:05:05.4846 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - DataTable作成完了: 9,986行 
2025-07-30 19:05:05.8828 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - BulkCopy完了: 9,986行 
2025-07-30 19:05:05.8828 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート完了 - 9,986件 
2025-07-30 19:05:05.8828 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理開始 
2025-07-30 19:05:05.9394 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - InventoryAll_wkレコード数: 9,986件 
2025-07-30 19:05:06.0261 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ店舗数: 121件 
2025-07-30 19:05:06.0261 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ商品数: 392,432件 
2025-07-30 19:05:06.2223 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合可能レコード数: 9,986件 
2025-07-30 19:05:06.4739 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合SQL実行結果: 9,986件処理 
2025-07-30 19:05:06.5572 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合後統計 - 在庫合計: 99,910, 平均在庫: 10.0 
2025-07-30 19:05:06.5572 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理完了 
2025-07-30 19:06:03.0324 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理完了 === 
2025-07-30 19:06:03.0324 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理時間: 74.2秒 
2025-07-30 19:06:03.0324 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - CSVレコード数: 9,986件 
2025-07-30 19:06:03.0324 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理レコード数: 2,786件 
2025-07-30 19:06:03.0324 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 在庫0補完数: 2,894件 
2025-07-30 19:06:03.0324 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 重複削除数: 10,094件 
2025-07-30 19:06:57.3649 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - データ確認用のワークデータ取得開始 
2025-07-30 19:06:57.7923 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - データ確認用のワークデータ取得完了: 2,786件（全件表示） 
2025-07-30 19:10:12.6309 [INFO] InventoryImportApp.Form1 - === アプリケーション開始 === 
2025-07-30 19:10:12.6439 [INFO] InventoryImportApp.Form1 - 実行ファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\ 
2025-07-30 19:10:12.6439 [INFO] InventoryImportApp.Form1 - ログファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\logs 
2025-07-30 19:10:17.1277 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理開始 === 
2025-07-30 19:10:17.1277 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 選択ファイル数: 1件 
2025-07-30 19:10:17.1277 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 登録日時: 2025-07-30 23:59:59 
2025-07-30 19:10:17.1277 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 対象ファイル: InventoryAll_363.csv (9,346件) 
2025-07-30 19:10:17.1277 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化開始 
2025-07-30 19:10:34.2691 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化完了 
2025-07-30 19:10:34.2691 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート開始 
2025-07-30 19:10:34.2691 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - CSVファイル読み込み開始: InventoryAll_363.csv (9,346行) 
2025-07-30 19:10:34.2994 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - DataTable作成完了: 9,346行 
2025-07-30 19:10:35.1226 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - BulkCopy完了: 9,346行 
2025-07-30 19:10:35.1226 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート完了 - 9,346件 
2025-07-30 19:10:35.1226 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理開始 
2025-07-30 19:10:35.1811 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - InventoryAll_wkレコード数: 9,346件 
2025-07-30 19:10:35.4725 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ店舗数: 121件 
2025-07-30 19:10:35.4725 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ商品数: 392,432件 
2025-07-30 19:10:35.8694 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合可能レコード数: 9,346件 
2025-07-30 19:10:36.2193 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合SQL実行結果: 9,346件処理 
2025-07-30 19:10:36.3025 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合後統計 - 在庫合計: 87,461, 平均在庫: 9.4 
2025-07-30 19:10:36.3025 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理完了 
2025-07-30 19:10:55.7968 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理完了 === 
2025-07-30 19:10:55.7968 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理時間: 38.7秒 
2025-07-30 19:10:55.7968 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - CSVレコード数: 9,346件 
2025-07-30 19:10:55.7968 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理レコード数: 5,747件 
2025-07-30 19:10:55.7968 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 在庫0補完数: 5,890件 
2025-07-30 19:10:55.7968 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 重複削除数: 9,489件 
2025-07-30 19:10:57.3609 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - データ確認用のワークデータ取得開始 
2025-07-30 19:10:58.7468 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - データ確認用のワークデータ取得完了: 5,747件（全件表示） 
2025-07-30 19:11:32.0455 [INFO] InventoryImportApp.Form1 - === アプリケーション開始 === 
2025-07-30 19:11:32.0609 [INFO] InventoryImportApp.Form1 - 実行ファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\ 
2025-07-30 19:11:32.0609 [INFO] InventoryImportApp.Form1 - ログファイルパス: C:\YSInventoryUpdate\src\InventoryImportApp\bin\Debug\net9.0-windows\logs 
2025-07-30 19:11:37.8106 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理開始 === 
2025-07-30 19:11:37.8106 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 選択ファイル数: 1件 
2025-07-30 19:11:37.8106 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 登録日時: 2025-07-30 23:59:59 
2025-07-30 19:11:37.8106 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 対象ファイル: InventoryAll_360.csv (9,266件) 
2025-07-30 19:11:37.8106 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化開始 
2025-07-30 19:11:54.7443 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ1: ワークテーブル初期化完了 
2025-07-30 19:11:54.7443 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート開始 
2025-07-30 19:11:54.7641 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - CSVファイル読み込み開始: InventoryAll_360.csv (9,266行) 
2025-07-30 19:11:54.7915 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - DataTable作成完了: 9,266行 
2025-07-30 19:11:55.5074 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - BulkCopy完了: 9,266行 
2025-07-30 19:11:55.5074 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ2: CSVファイルインポート完了 - 9,266件 
2025-07-30 19:11:55.5074 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理開始 
2025-07-30 19:11:55.5919 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - InventoryAll_wkレコード数: 9,266件 
2025-07-30 19:11:55.7287 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ店舗数: 121件 
2025-07-30 19:11:55.7287 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - アクティブ商品数: 392,432件 
2025-07-30 19:11:56.0562 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合可能レコード数: 9,266件 
2025-07-30 19:11:56.4074 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合SQL実行結果: 9,266件処理 
2025-07-30 19:11:56.4856 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - マスタ結合後統計 - 在庫合計: 110,673, 平均在庫: 11.9 
2025-07-30 19:11:56.4856 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - フェーズ3: 基本マスタ結合処理完了 
2025-07-30 19:12:30.1268 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - === インポート処理完了 === 
2025-07-30 19:12:30.1268 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理時間: 52.3秒 
2025-07-30 19:12:30.1268 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - CSVレコード数: 9,266件 
2025-07-30 19:12:30.1268 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 処理レコード数: 141件 
2025-07-30 19:12:30.1268 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 在庫0補完数: 253件 
2025-07-30 19:12:30.1268 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - 重複削除数: 9,378件 
2025-07-30 19:12:31.6746 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - データ確認用のワークデータ取得開始 
2025-07-30 19:12:31.9256 [INFO] InventoryImportApp.Core.Services.DatabaseImportService - データ確認用のワークデータ取得完了: 141件（全件表示） 
